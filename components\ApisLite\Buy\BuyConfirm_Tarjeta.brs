sub DataInit()
    m.top.debug = true

    if Instr(1, m.top.buylink, "buyconfirm") > 0 then
    m.llamado = "buyconfirm"

    m.api.method = "POST"
    m.api.url = m.config.mfwk.host + m.top.buylink

    m.api.query.delete("api_version")

    m.api.query.Append({
        "user_id": ghGetRegistry("user_id", "user"),
    })
    m.api.timeout = 30000

    ' default
    if m.top.extraParams <> invalid then
        extra_params = FormatJson(m.top.extraParams)
    else
        extra_params = ""
    end if

    m.api.body = ghArray2Query({
        "token": ghGetRegistry("user_token", "user")
        "buyToken": m.top.buyToken
        "extra_params": extra_params
    }, "")
    end if
end sub

sub ProcessData(res, raw)
    ?"response here is ==>"formatJSON(res)

end sub