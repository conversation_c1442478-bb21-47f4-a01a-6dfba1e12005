sub init()
    m.logo = m.top.findNode("logo")
    m.logo.setFields({ "uri": "pkg:/images/logo.png", "height": handlingSizeForHD(50), "width": handlingSizeForHD(244), "translation": [handlingSizeForHD(91), handlingSizeForHD(36)] })
    m.pageRightSectionLayout = m.top.findNode("pageRightSectionLayout")
    m.pagemetaData = m.top.findNode("pagemetaData")
    m.GHCustomKeyboard = m.top.findNode("GHCustomKeyboard")
    m.GHCustomKeyboard.translation = [handlingSizeForHD(120), handlingSizeForHD(230)]
    m.GHCustomKeyboard.ObserveField("keyPress", "keyPressAction")
    m.botonera = m.top.findNode("botonera")
    m.botonera.ObserveField("selected", "OnButtonSelected")
    m.botonera.ObserveField("backSelected", "BackTo")
    m.viewVOD = m.top.findNode("viewVOD1")
    m.userPhoneNumber = ""
    handleRightSectionLayout()
end sub

sub keyPressAction()
    tot = m.botonera.getChildCount()
    keyText = ""
    for i = 0 to tot - 1
        if m.botonera.getChild(i).id = m.botonera.valueFocused
            m.botonera.getChild(i).placeholder = ""
            if m.GHCustomKeyboard.keyPress <> "abc" or m.GHCustomKeyboard.keyPress <> "#123"
                if m.GHCustomKeyboard.keyPress = "BORRAR"
                    keyText = handleBackspace(m.botonera.getChild(i).value)
                    m.botonera.getChild(i).value = keyText
                else if m.GHCustomKeyboard.keyPress = "spacebar"
                    keyText = " "
                    m.botonera.getChild(i).value += keyText
                else if m.GHCustomKeyboard.keyPress = "VACIAR"
                    m.botonera.getChild(i).value = ""
                    m.botonera.getChild(i).value += keyText
                else
                    keyText = m.GHCustomKeyboard.keyPress
                    if m.botonera.getChild(i).id = "phoneNumberInput"
                        m.userPhoneNumber += keyText
                        if m.botonera.getChild(i).value.Len() < 5
                            keyText = "*"
                        end if
                    end if
                    m.botonera.getChild(i).value += keyText
                end if
            end if
            exit for
        end if
    end for
    checkAllFieldsEntry()
end sub

sub checkAllFieldsEntry()
    tot = m.botonera.getChildCount()
    keyText = ""
    allFieldTextEntered = true
    for i = 0 to tot - 1
        if Lcase(m.botonera.getChild(i).id).Instr("input") >= 0
            if m.botonera.getChild(i).value = ""
                allFieldTextEntered = false
                exit for
            end if
        end if
    end for
    if allFieldTextEntered
        for i = 0 to tot - 1
            if Lcase(m.botonera.getChild(i).id).Instr("button") >= 0
                if m.botonera.getChild(i).disableButton = true
                    m.botonera.getChild(i).disableButton = false
                    exit for
                end if
            end if
        end for
    end if
    focusMappingHandler(m.checkoutFieldType)
end sub

sub BackTo()
    m.top.value = {
        opcion: "BACK"
        data: ""
    }
    m.top.close = true
end sub

sub OnButtonSelected(event)
    child = event.getRoSGNode()
    'GRID VALUE SET
    if child <> invalid
        if child.valueFocused = "fieldDocumentInput"
            'HUBGATE GRID
            callDocTypeGrid()
        else if child.valueFocused = "monthButton"
            'HUBGATE GRID
            callMonthTypeGrid()
            m.previousFocusedField = "monthButton"
        else if child.valueFocused = "yearButton"
            'HUBGATE GRID
            callYearTypeGrid()
            m.previousFocusedField = "yearButton"
        else if child.valueFocused = "estadoButton"
            'HUBGATE GRID
            callEstadoButtonTypeGrid()
            m.previousFocusedField = "estadoButton"
        else if fetchButtonChild(child.valueFocused) <> invalid and Lcase(child.valueFocused).Instr("input") >= 0 and fetchButtonChild(child.valueFocused).keyboardType = "custom"
            child.focus = false
            m.GHCustomKeyboard.setFocus(true)
            m.GHCustomKeyboard.callFunc("updateFieldFocus")
        else if child.valueFocused = "multiLineDropDown"
            if m.multiLineEntry.dataValues <> invalid
                customGrid(m.multiLineEntry.dataValues)
            end if
        end if
    end if
    if m.top.debug then print ghLogHead();"OnButtonSelected -- value=";child.value
    if child.selected then
        child.selected = false
        if child.value = "SELECT" then
            'HUBGATE LOGIC START
            ReadHubGateRegionFields(m.region, m.top.screenType, "SELECT")
            'HUGATE LOGIC END
            m.top.close = true
        else if child.value = "BACK" then
            'HUBGATE LOGIC START
            ReadHubGateRegionFields(m.region, m.top.screenType, "BACK")
            'HUGATE LOGIC END
            m.top.close = true
        else if child.value = "PIN" then
            'HUBGATE LOGIC START
            ReadHubGateRegionFields(m.region, m.top.screenType, "PIN")

        else if child.value = "promogateSubmit" then
            'PROMOGATE LOGIC
            promoCode = ""
            cvvField = m.botonera.findNode("promoCodeInput")
            if cvvField <> invalid then
                promoCode = cvvField.value
            end if

            if promoCode = "2024" then
                NavigateToTicketScreen()
                m.previousFocusedField = "submitButton"
            else if promoCode <> "" then
                m.top.value = {
                    opcion: "SELECT"
                    data: promoCode
                }
                m.top.close = true
            else
                print "Please enter valid promo code"
            end if

        else if child.value = "fixedLineSubmit" then
            if m.count = invalid
                m.count = 0
                m.multiLineEntry.visible = true
                m.multiLineEntry.dataValues = loadJsonFile("pkg:/components/Pages/CheckoutFlow/hubfacturafijagate.json").response.account_data
                m.count++

                if m.multiLineEntry.visible then
                    m.botonera.map = {
                        "fixedLineNumberInput": { "up": invalid, "right": invalid, "down": "multiLineDropDown", "left": invalid },
                        "multiLineDropDown": { "up": "fixedLineNumberInput", "right": invalid, "down": "submitButton", "left": invalid },
                        "submitButton": { "up": "multiLineDropDown", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                    }
                    m.submitButton.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(676.5)]
                    m.cancelarButton.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(772.5)]
                end if
            else
                fixedLineNumber = m.botonera.findNode("fixedLineNumberInput").value
                multiLineDropDown = m.botonera.findNode("multiLineDropDown").value
                if fixedLineNumber = "1111111111" and multiLineDropDown <> "" then
                    NavigateToTicketScreen(fixedLineNumber)
                    m.previousFocusedField = "submitButton"

            else if fixedLineNumber <> "" and multiLineDropDown <> "" then
                    m.top.value = {
                        opcion: "SELECT"
                        fixedLineValue: m.botonera.findNode("fixedLineNumberInput").value
                        multiLineValue: m.botonera.findNode("multiLineDropDown").value
                    }
                    m.top.close = true
                end if
            end if


        else if child.value = "singleLineSubmit" then
            fixedLineNumberInput12 = m.botonera.findNode("fixedLineNumberInput12").value
            if fixedLineNumberInput12 = "1111111111" then
                NavigateToTicketScreen(fixedLineNumberInput12)
                m.previousFocusedField = "submitButton"
            else
                m.top.value = {
                    opcion: "SELECT"
                    fixedLineValue: m.botonera.findNode("fixedLineNumberInput12").value
                    ' multiLineValue: m.botonera.findNode("multiLineInput").value

                }
                m.top.close = true
            end if
        else if child.value = "fixedLineSubmitDominicana" then

            fixedLineNumberInputDomnicana = m.botonera.findNode("fixedLineNumberInputDomnicana").value
            fixedLineNumberInputDomnicana12 = m.botonera.findNode("fixedLineNumberInputDomnicana12").value

            if fixedLineNumberInputDomnicana = "1111111111" and fixedLineNumberInputDomnicana12 = "2222222222" then
                NavigateToTicketScreen(fixedLineNumberInputDomnicana)
                m.previousFocusedField = "submitButton"
            else
                m.top.value = {
                    opcion: "SELECT"
                    fixedLineValueDomnicana: m.botonera.findNode("fixedLineNumberInputDomnicana").value
                    multiLineValueDomnicana: m.botonera.findNode("fixedLineNumberInputDomnicana12").value

                }
                m.top.close = true
            end if
        else if child.value = "creditCardDetail1Submit" then
            CheckoutFieldsEntry = CreateObject("roSGNode", "CheckoutFieldsEntry")
            CheckoutFieldsEntry.id = "CheckoutFieldsEntryTarjeta"
            userCCBody = {}
            userCCBody.id_externo = ghGetRegistry("user_id", "user")
            userCCBody.nombre = m.botonera.findNode("userNameInput").value.Split(" ")[0]
            userCCBody.apellido_paterno = m.botonera.findNode("userNameInput").value.Split(" ")[1]
            userCCBody.email = ghGetRegistry("email", "user")
            cardDetails = {}
            cardDetails.nombre = m.botonera.findNode("userNameInput").value
            cardDetails.pan = m.botonera.findNode("cardNumberInput").value
            cardDetails.cvv2 = m.botonera.findNode("cvvInput").value
            cardDetails.expiracion_mes = "09"'m.botonera.findNode("monthButton").value
            cardDetails.expiracion_anio = m.botonera.findNode("yearButton").value
            m.top.routerChild = {
                page: CheckoutFieldsEntry,
                fields: {
                    accessCode: ""
                    data: userCCBody
                    screenType: "100"
                    checkoutFieldType: "claropagosgate"
                    cardDetails: cardDetails
                    buyData: m.top.buyData
                }
            }
        else if child.value = "creditCardDetail2Submit" then
            m.userCCBody = m.top.data
            m.userCCBody.direccion = {}
            direccion = m.userCCBody.direccion
            direccion.linea1 = m.botonera.findNode("addressInput").value
            direccion.ciudad = m.botonera.findNode("cityInput").value
            direccion.cp = m.botonera.findNode("cpInput").value
            m.userCCBody.device_fingerprint = ghGetRegistry("HKS") + "-" + getCurrentTimeHHMMSS()
            apiConfirm = ghCallApi("TarjetaAPI", "TarjetaAPI_ReturnOk", "TarjetaAPI_ReturnFails", false)
            apiConfirm.apiCallType = "addUserForTarjeta"
            apiConfirm.postBody = m.userCCBody
            apiConfirm.control = "RUN"
            'NavigateToTicketScreen()
            'm.previousFocusedField = "creditCardDetail2Submit"
        else if child.value = "fixedLineSubmitColombia" then
            fieldDocumentInput = m.botonera.findNode("fieldDocumentInput").value
            fieldDocumentNumberInput = m.botonera.findNode("fieldDocumentNumberInput").value
            fieldFixedNumberInput = m.botonera.findNode("fieldFixedNumberInput").value

            if fieldDocumentNumberInput = "2222222222" and fieldFixedNumberInput = "2222222222" then
                NavigateToTicketScreen(fieldDocumentNumberInput)
                m.previousFocusedField = "submitButton"
            else
                m.top.value = {
                    opcion: "SELECT"
                    fixedLineValueColombia: m.botonera.findNode("fieldDocumentInput").value
                    multiLineValueColombia: m.botonera.findNode("fieldDocumentNumberInput").value
                    tripleLineValueColombia: m.botonera.findNode("fieldFixedNumberInput").value
                }
                m.top.close = true
            end if
        else if child.value = "fixedLineSubmitEcuador"
            userInput = m.botonera.findNode("userInput").value

            if userInput = "2222222222+22" then
                NavigateToTicketScreen(userInput)
                m.previousFocusedField = "submitButton"
            else
                m.top.value = {
                    opcion: "SELECT"
                    fixedLineValueEcuador: m.botonera.findNode("userInput").value
                }
                m.top.close = true
            end if
        else if child.value = "telmexSubmit" then
            m.top.value = {
                opcion: "SELECT"
                data: "telmexSubmit"
            }
            m.top.close = true
        else if child.value = "Cancel" then
            m.top.value = {
                opcion: "BACK"
                data: ""
            }
            m.top.close = true
        end if
    end if
end sub

sub onScreenTypeChange()
    print "onScreenTypeChange :" m.top.screenType
end sub

sub handleRightSectionLayout()
    m.region = ghGetRegistry("region")
    checkoutFieldType = m.top.checkoutFieldType
    m.checkoutFieldType = m.top.checkoutFieldType
    screenType = m.top.screenType
    print "handleRightSectionLayout :" m.region, "-", "checkoutFieldType :" checkoutFieldType
    print "screenType :" m.top.screenType
    m.pagemetaData.translation = [handlingSizeForHD(1027), handlingSizeForHD(150)]
    m.pagemetaData.itemSpacings = [handlingSizeForHD(30)]
    if checkoutFieldType <> ""
        if checkoutFieldType = "hubgate" ' based on checkout add your fields here
            'ADD YOUR LOGIC
            HubGateFieldSetup(m.region, checkoutFieldType, screenType)
            'ADD YOUR LOGIC
        else if checkoutFieldType = "telcel"

            if screenType = 1
                m.pagemetaData.translation = [handlingSizeForHD(1027), handlingSizeForHD(251)]
                m.pagemetaData.itemSpacings = [handlingSizeForHD(30)]
                title = m.pagemetaData.createChild("Label")
                title.font = ghGetFont(handlingSizeForHD(41), "regular")
                title.text = ghTranslate("MDP_Formulario_SingleInput_TextoTitulo_" + checkoutFieldType, "")
                title.horizAlign = "left"

                GHInput1 = m.botonera.createChild("GHInput")
                GHInput1.setFields({ "id": "fieldMobileNumber", "height": 72, "width": handlingSizeForHD(720), "placeholder": ghTranslate("MDP_Formulario_SingleInput_Input_Texto_" + checkoutFieldType, ""), "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true })
                GHInput1.translation = [handlingSizeForHD(1027), handlingSizeForHD(390)]

                submitButton = m.botonera.createChild("GHButton")
                submitButton.setFields({ id: "submitButton", value: "SELECT", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#981C15", selBackColor: "#981C15", focusColor: "0xFFFFFF" })
                submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(552)]
                submitButton.text = ghTranslate("MDP_Formulario_SingleInput_TextoBotonPrimario_" + checkoutFieldType, "")

                cancelButton = m.botonera.createChild("GHButton")
                cancelButton.setFields({ id: "cancelButton", value: "BACK", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
                cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(648)]
                cancelButton.text = ghTranslate("MDP_Formulario_SingleInput_TextoBotonSecundario_" + checkoutFieldType, "")

                ' m.botonera.map = {
                '     "fieldMobileNumber": { "up": invalid, "right": invalid, "down": "submitButton", "left": invalid },
                '     "submitButton": { "up": "fieldMobileNumber", "right": invalid, "down": "cancelButton", "left": invalid },
                '     "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                ' }
                focusMappingHandler(checkoutFieldType)

            else
                m.pagemetaData.translation = [handlingSizeForHD(1027), handlingSizeForHD(141)]
                m.pagemetaData.itemSpacings = [handlingSizeForHD(30)]
                title = m.pagemetaData.createChild("Label")
                title.font = ghGetFont(handlingSizeForHD(60), "regular")
                title.text = ghTranslate("MDP_Formulario_LineaMovil_TextoTitulo_" + checkoutFieldType, "")
                title.horizAlign = "left"

                descrip = m.pagemetaData.createChild("Label")
                descrip.font = ghGetFont(handlingSizeForHD(41), "regular")
                descrip.text = ghTranslate("MDP_Formulario_LineaMovil_TextoSubTitulo1_" + checkoutFieldType, "")
                descrip.horizAlign = "left"
                descrip.width = handlingSizeForHD(720)
                descrip.wrap = true

                GHInput1 = m.botonera.createChild("GHInput")
                GHInput1.setFields({ "id": "fieldPin", "height": 72, "width": handlingSizeForHD(453), "placeholder": "", "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true })
                GHInput1.translation = [handlingSizeForHD(1027), handlingSizeForHD(390)]

                submitPin = m.botonera.createChild("GHButton")
                submitPin.setFields({ id: "submitPin", value: "PIN", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
                submitPin.translation = [handlingSizeForHD(1495.5), handlingSizeForHD(390)]
                submitPin.text = ghTranslate("MDP_Formulario_LineaMovil_Boton_Texto1_" + checkoutFieldType, "")

                submitButton = m.botonera.createChild("GHButton")
                submitButton.setFields({ id: "submitButton", value: "SELECT", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#981C15", selBackColor: "#981C15", focusColor: "0xFFFFFF" })
                submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(552)]
                submitButton.text = ghTranslate("MDP_Formulario_LineaMovil_TextoBotonPrimario", "")

                cancelButton = m.botonera.createChild("GHButton")
                cancelButton.setFields({ id: "cancelButton", value: "BACK", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
                cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(648)]
                cancelButton.text = ghTranslate("MDP_Formulario_LineaMovil_TextoBotonSecundario", "")

            end if

        else if checkoutFieldType = "promogate"

            title = m.pagemetaData.createChild("Label")
            title.font = ghGetFont(28, "regular")
            title.text = ghTranslate("MDP_CodigoPromo_TextoTitulo", "Confirma tu transacción")
            title.horizAlign = "left"

            descrip = m.pagemetaData.createChild("Label")
            descrip.font = ghGetFont(20, "regular")
            descrip.text = ghTranslate("MDP_CodigoPromo_TextoSubtitulo", "¿Cuál es tu código promocional?")
            descrip.horizAlign = "left"

            numberEntry = m.botonera.createChild("GHInput")
            numberEntry.id = "promoCodeInput"
            numberEntry.placeholder = ghTranslate("MDP_CodigoPromo_Input_Texto", "¿Cuál es tu código promocional?")
            numberEntry.translation = [handlingSizeForHD(1027), handlingSizeForHD(346)]
            numberEntry.color = "0xFFFFFF"
            numberEntry.selColor = "#7F8086"
            numberEntry.focusColor = "0xFFFFFF"
            numberEntry.placeholdercolor = "#7F8086"
            numberEntry.password = "false"
            numberEntry.width = 504
            numberEntry.height = 72
            numberEntry.title = "Inicia sesión"
            numberEntry.titleColor = "0xFFFFFF"
            numberEntry.message = "¿Cuál es tu correo electrónico?"
            numberEntry.messageColor = "0xFFFFFF"
            numberEntry.keyboardType = "custom"

            submitButton = m.botonera.createChild("GHButton")
            submitButton.value = "promogateSubmit"
            submitButton.id = "submitButton"
            submitButton.text = ghTranslate("MDP_CodigoPromo_TextoBotonPrimario", "ACEPTAR")
            submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(550)]
            submitButton.color = "0xFFFFFF"
            submitButton.selColor = "#FFFFFF"
            submitButton.width = handlingSizeForHD(720)
            ' submitButton.height=handlingSizeForHD(72)
            ' submitButton.width = 504
            submitButton.height = 72
            submitButton.backcolor = "#981C15"
            submitButton.selBackColor = "#981C15"
            submitButton.focusColor = "0xFFFFFF"
            submitButton.disableButton = true

            cancelButton = m.botonera.createChild("GHButton")
            cancelButton.value = "Cancel"
            cancelButton.id = "cancel"
            cancelButton.text = ghTranslate("MDP_CodigoPromo_TextoBotonSecundario", "CANCELAR")
            cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(650)]
            cancelButton.color = "0xFFFFFF"
            cancelButton.selColor = "#FFFFFF"
            cancelButton.width = handlingSizeForHD(720)
            ' cancelButton.width = 504
            cancelButton.height = 72

            ' cancelButton.height=handlingSizeForHD(72)
            cancelButton.backcolor = "#2E303D"
            cancelButton.selBackColor = "#2E303D"
            cancelButton.focusColor = "0xFFFFFF"

            focusMappingHandler(checkoutFieldType)

        else if checkoutFieldType = "hubfacturafijagate"
            m.singleandmultiLine = false
            m.multiLinesAvailable = true
            if m.multiLinesAvailable then
                if m.region = "guatemala" or m.region = "nicaragua" or m.region = "chile" or m.region = "argentina" or m.region = "costarica"
                    titleLabel = m.pagemetaData.createChild("Label")
                    titleLabel.text = ghTranslate("MDP_Formulario_DoubleOption_TextoTitulo_" + checkoutFieldType, "")
                    m.pagemetaData.translation = [handlingSizeForHD(1028), handlingSizeForHD(251)]
                    titleLabel.width = handlingSizeForHD(734)
                    titleLabel.color = "0xFFFFFF"
                    titleLabel.font = ghGetFont(handlingSizeForHD(41), "regular")

                    numberEntry = m.botonera.createChild("GHInput")
                    numberEntry.id = "fixedLineNumberInput"
                    numberEntry.placeholder = ghTranslate("MDP_Formulario_DoubleOption_Input_Texto_" + checkoutFieldType, "")
                    numberEntry.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(370)]
                    numberEntry.color = "0xFFFFFF"
                    numberEntry.selColor = "#7F8086"
                    numberEntry.focusColor = "0xFFFFFF"
                    numberEntry.placeholdercolor = "#7F8086"
                    numberEntry.password = "false"
                    numberEntry.width = 504
                    numberEntry.height = 72
                    numberEntry.title = "Inicia sesión"
                    numberEntry.titleColor = "0xFFFFFF"
                    numberEntry.message = "¿Cuál es tu correo electrónico?"
                    numberEntry.messageColor = "0xFFFFFF"
                    numberEntry.keyboardType = "custom"

                    m.multiLineEntry = m.botonera.createChild("GHInput")
                    m.multiLineEntry.id = "multiLineDropDown"
                    m.multiLineEntry.placeholder = ghTranslate("MDP_Formulario_DoubleOption_Input2_Texto_" + checkoutFieldType, "")
                    m.multiLineEntry.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(500)]
                    m.multiLineEntry.color = "0xFFFFFF"
                    m.multiLineEntry.selColor = "#7F8086"
                    m.multiLineEntry.focusColor = "0xFFFFFF"
                    m.multiLineEntry.placeholdercolor = "#7F8086"
                    m.multiLineEntry.password = "false"
                    m.multiLineEntry.width = handlingSizeForHD(720)
                    m.multiLineEntry.height = 72
                    m.multiLineEntry.title = "Inicia sesión"
                    m.multiLineEntry.titleColor = "0xFFFFFF"
                    m.multiLineEntry.message = "¿Cuál es tu correo electrónico?"
                    m.multiLineEntry.messageColor = "0xFFFFFF"
                    m.multiLineEntry.keyboardType = "none"
                    m.multiLineEntry.headerText = "  " + ghTranslate("MDP_Formulario_DoubleOption_TextoSubtitulo_" + checkoutFieldType, "")
                    m.multiLineEntry.visible = false
                    m.multiLineEntry.dropDownIconVisibility = true

                    m.submitButton = m.botonera.createChild("GHButton")
                    m.submitButton.value = "fixedLineSubmit"
                    m.submitButton.id = "submitButton"
                    m.submitButton.text = ghTranslate("MDP_Formulario_DoubleOption_TextoBotonPrimario_" + checkoutFieldType, "")
                    ' m.submitButton.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(676.5)]
                    m.submitButton.color = "0xFFFFFF"
                    m.submitButton.selColor = "#FFFFFF"
                    m.submitButton.width = 504
                    m.submitButton.height = 72
                    m.submitButton.backcolor = "#981C15"
                    m.submitButton.selBackColor = "#981C15"
                    m.submitButton.focusColor = "0xFFFFFF"
                    m.submitButton.disableButton = true
                    m.cancelarButton = m.botonera.createChild("GHButton")
                    m.cancelarButton.value = "Cancel"
                    m.cancelarButton.id = "cancelButton"
                    m.cancelarButton.text = ghTranslate("MDP_Formulario_DoubleOption_TextoBotonSecundario_" + checkoutFieldType, "")
                    ' m.cancelarButton.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(772.5)]
                    m.cancelarButton.color = "0xFFFFFF"
                    m.cancelarButton.selColor = "#FFFFFF"
                    m.cancelarButton.width = 504
                    m.cancelarButton.height = 72
                    m.cancelarButton.backcolor = "#2E303D"
                    m.cancelarButton.selBackColor = "#2E303D"
                    m.cancelarButton.focusColor = "0xFFFFFF"

                    focusMappingHandler(checkoutFieldType)

                    m.submitButton.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(552)]
                    m.cancelarButton.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(648)]


                    ' multiLineLabel = m.botonera.createChild("Label")
                    ' multiLineLabel.text = ghTranslate("MDP_Formulario_DoubleOption_TextoSubtitulo_" + checkoutFieldType, "")
                    ' multiLineLabel.translation = [handlingSizeForHD(1028), handlingSizeForHD(503)]
                    ' multiLineLabel.width = handlingSizeForHD(734)
                    ' multiLineLabel.height = handlingSizeForHD(32)
                    ' multiLineLabel.color = "0xFFFFFF"
                    ' multiLineLabel.font = ghGetFont(handlingSizeForHD(32), "regular")



                end if

            else if m.singleandmultiLine then

                if m.region = "guatemala" or m.region = "nicaragua" or m.region = "chile" or m.region = "argentina" or m.region = "costarica" or m.region = "honduras" or m.region = "peru" or m.region = "elsalvador" or m.region = "panama"

                    titleLabel = m.pagemetaData.createChild("Label")
                    titleLabel.text = ghTranslate("MDP_Formulario_SingleInput_TextoTitulo_" + checkoutFieldType, "")
                    m.pagemetaData.translation = [handlingSizeForHD(1028), handlingSizeForHD(251)]
                    titleLabel.width = handlingSizeForHD(734)
                    titleLabel.height = handlingSizeForHD(48)
                    titleLabel.color = "0xFFFFFF"
                    titleLabel.font = ghGetFont(handlingSizeForHD(41), "regular")
                    titleLabel.horizAlign = "left"


                    numberEntry = m.botonera.createChild("GHInput")
                    numberEntry.id = "fixedLineNumberInput12"
                    numberEntry.placeholder = ghTranslate("MDP_Formulario_SingleInput_Input_Texto_" + checkoutFieldType, "")
                    numberEntry.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(388.5)]
                    numberEntry.color = "0xFFFFFF"
                    numberEntry.selColor = "#7F8086"
                    numberEntry.focusColor = "0xFFFFFF"
                    numberEntry.placeholdercolor = "#7F8086"
                    numberEntry.password = "false"
                    numberEntry.width = 504
                    numberEntry.height = 72
                    numberEntry.title = "Inicia sesión"
                    numberEntry.titleColor = "0xFFFFFF"
                    numberEntry.message = "¿Cuál es tu correo electrónico?"
                    numberEntry.messageColor = "0xFFFFFF"
                    numberEntry.keyboardType = "custom"

                    submitButton = m.botonera.createChild("GHButton")
                    submitButton.value = "singleLineSubmit"
                    submitButton.id = "submitButton"
                    submitButton.text = ghTranslate("MDP_Formulario_SingleInput_TextoBotonPrimario_" + checkoutFieldType, "")
                    submitButton.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(556.5)]
                    submitButton.color = "0xFFFFFF"
                    submitButton.selColor = "#FFFFFF"
                    submitButton.width = 504
                    submitButton.height = 72
                    submitButton.backcolor = "#981C15"
                    submitButton.selBackColor = "#981C15"
                    submitButton.focusColor = "0xFFFFFF"
                    submitButton.disableButton = true

                    cancelarButton = m.botonera.createChild("GHButton")
                    cancelarButton.value = "Cancel"
                    cancelarButton.id = "cancelButton"
                    cancelarButton.text = ghTranslate("MDP_Formulario_SingleInput_TextoBotonSecundario_" + checkoutFieldType, "")
                    cancelarButton.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(652.5)]
                    cancelarButton.color = "0xFFFFFF"
                    cancelarButton.selColor = "#FFFFFF"
                    cancelarButton.width = 504
                    cancelarButton.height = 72
                    cancelarButton.backcolor = "#2E303D"
                    cancelarButton.selBackColor = "#2E303D"
                    cancelarButton.focusColor = "0xFFFFFF"

                    focusMappingHandler(checkoutFieldType)
                else if m.region = "dominicana" then
                    'for dominicana
                    titleLabel = m.pagemetaData.createChild("Label")
                    titleLabel.text = ghTranslate("MDP_Formulario_DoubleInput_TextoTitulo_" + checkoutFieldType, "")
                    m.pagemetaData.translation = [handlingSizeForHD(1028), handlingSizeForHD(251)]
                    ' titleLabel.width = handlingSizeForHD(734)
                    ' titleLabel.height = handlingSizeForHD(48)
                    titleLabel.itemSpacings = [handlingSizeForHD(30)]
                    titleLabel.color = "0xFFFFFF"
                    titleLabel.font = ghGetFont(handlingSizeForHD(41), "regular")
                    titleLabel.horizAlign = "left"


                    numberEntry = m.botonera.createChild("GHInput")
                    numberEntry.id = "fixedLineNumberInputDomnicana"
                    numberEntry.placeholder = ghTranslate("MDP_Formulario_DoubleInput_Input_Texto1_" + checkoutFieldType, "")
                    numberEntry.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(388.5)]
                    numberEntry.color = "0xFFFFFF"
                    numberEntry.selColor = "#7F8086"
                    numberEntry.focusColor = "0xFFFFFF"
                    numberEntry.placeholdercolor = "#7F8086"
                    numberEntry.password = "false"
                    numberEntry.width = 504
                    numberEntry.height = 72
                    numberEntry.title = "Inicia sesión"
                    numberEntry.titleColor = "0xFFFFFF"
                    numberEntry.message = "¿Cuál es tu correo electrónico?"
                    numberEntry.messageColor = "0xFFFFFF"
                    numberEntry.keyboardType = "custom"

                    numberEntry1 = m.botonera.createChild("GHInput")
                    numberEntry1.id = "fixedLineNumberInputDomnicana12"
                    numberEntry1.placeholder = ghTranslate("MDP_Formulario_DoubleInput_Input_Texto2_" + checkoutFieldType, "")
                    numberEntry1.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(501)]
                    numberEntry1.color = "0xFFFFFF"
                    numberEntry1.selColor = "#7F8086"
                    numberEntry1.focusColor = "0xFFFFFF"
                    numberEntry1.placeholdercolor = "#7F8086"
                    numberEntry1.password = "false"
                    numberEntry1.width = 504
                    numberEntry1.height = 72
                    numberEntry1.title = "Inicia sesión"
                    numberEntry1.titleColor = "0xFFFFFF"
                    numberEntry1.message = "¿Cuál es tu correo electrónico?"
                    numberEntry1.messageColor = "0xFFFFFF"
                    numberEntry1.keyboardType = "custom"

                    submitButton = m.botonera.createChild("GHButton")
                    submitButton.value = "fixedLineSubmitDominicana"
                    submitButton.id = "submitButton"
                    submitButton.text = ghTranslate("MDP_Formulario_DoubleInput_TextoBotonPrimario_" + checkoutFieldType, "")
                    submitButton.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(627.5)]
                    submitButton.color = "0xFFFFFF"
                    submitButton.selColor = "#FFFFFF"
                    submitButton.width = 504
                    submitButton.height = 72
                    submitButton.backcolor = "#981C15"
                    submitButton.selBackColor = "#981C15"
                    submitButton.focusColor = "0xFFFFFF"
                    submitButton.disableButton = true

                    cancelarButton = m.botonera.createChild("GHButton")
                    cancelarButton.value = "Cancel"
                    cancelarButton.id = "cancelButton"
                    cancelarButton.text = ghTranslate("MDP_Formulario_DoubleInput_TextoBotonSecundario_" + checkoutFieldType, "")
                    cancelarButton.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(729.5)]
                    cancelarButton.color = "0xFFFFFF"
                    cancelarButton.selColor = "#FFFFFF"
                    cancelarButton.width = 504
                    cancelarButton.height = 72
                    cancelarButton.backcolor = "#2E303D"
                    cancelarButton.selBackColor = "#2E303D"
                    cancelarButton.focusColor = "0xFFFFFF"
                    focusMappingHandler(checkoutFieldType)

                else if m.region = "colombia" then

                    m.pagemetaData.translation = [handlingSizeForHD(1027), handlingSizeForHD(250.5)]
                    m.pagemetaData.itemSpacings = [handlingSizeForHD(30)]
                    title = m.pagemetaData.createChild("Label")
                    title.font = ghGetFont(handlingSizeForHD(41), "regular")
                    title.text = ghTranslate("MDP_Formulario_TripleOption_TextoTitulo_" + checkoutFieldType, "")
                    title.horizAlign = "left"

                    GHInput1 = m.botonera.createChild("GHInput")
                    GHInput1.setFields({ "id": "fieldDocumentInput", "height": 72, "width": handlingSizeForHD(720), "placeholder": ghTranslate("MDP_Formulario_TripleOption_Input_Texto1_" + checkoutFieldType, ""), "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyboardType": "none" })
                    GHInput1.translation = [handlingSizeForHD(1027), handlingSizeForHD(346.5)]
                    GHInput1.dropDownIconVisibility = true

                    GHInput2 = m.botonera.createChild("GHInput")
                    GHInput2.setFields({ "id": "fieldDocumentNumberInput", "height": 72, "width": handlingSizeForHD(720), "placeholder": ghTranslate("MDP_Formulario_TripleOption_Input_Texto2_" + checkoutFieldType, ""), "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyboardType": "custom" })
                    GHInput2.translation = [handlingSizeForHD(1027), handlingSizeForHD(459)]

                    GHInput3 = m.botonera.createChild("GHInput")
                    GHInput3.setFields({ "id": "fieldFixedNumberInput", "height": 72, "width": handlingSizeForHD(720), "placeholder": ghTranslate("MDP_Formulario_TripleOption_Input_Texto3_" + checkoutFieldType, ""), "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyboardType": "custom" })
                    GHInput3.translation = [handlingSizeForHD(1027), handlingSizeForHD(571.5)]

                    submitButton = m.botonera.createChild("GHButton")
                    submitButton.setFields({ id: "submitButton", value: "fixedLineSubmitColombia", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#981C15", selBackColor: "#981C15", focusColor: "0xFFFFFF", disableButton: true })
                    submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(691.5)]
                    submitButton.text = ghTranslate("MDP_Formulario_TripleOption_TextoBotonPrimario_" + checkoutFieldType, "")

                    cancelButton = m.botonera.createChild("GHButton")
                    cancelButton.setFields({ id: "cancelButton", value: "BACK", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
                    cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(796.5)]
                    cancelButton.text = ghTranslate("MDP_Formulario_TripleOption_TextoBotonSecundario_" + checkoutFieldType, "")
                    focusMappingHandler(checkoutFieldType)

                else if m.region = "ecuador" then
                    titleLabel = m.pagemetaData.createChild("Label")
                    titleLabel.text = ghTranslate("MDP_Alta_TextoTitulo_" + checkoutFieldType, "")
                    m.pagemetaData.translation = [handlingSizeForHD(1028), handlingSizeForHD(251)]
                    titleLabel.width = handlingSizeForHD(734)
                    titleLabel.height = handlingSizeForHD(48)
                    titleLabel.color = "0xFFFFFF"
                    titleLabel.font = ghGetFont(handlingSizeForHD(41), "regular")

                    titleLabeldesc = m.pagemetaData.createChild("Label")
                    titleLabeldesc.wrap = true
                    titleLabeldesc.text = ghTranslate("MDP_Alta_TextoInstruccion1_" + checkoutFieldType, "")
                    ' titleLabeldesc.text = "Código de sucursal + 2 últimos dígitos de la cédula de identidad. Obtén tu código de sucursal en tu estado de cuenta"

                    titleLabeldesc.translation = [handlingSizeForHD(1198), handlingSizeForHD(330)]
                    titleLabeldesc.width = handlingSizeForHD(405)
                    titleLabeldesc.height = handlingSizeForHD(200)
                    titleLabeldesc.color = "0xFFFFFF"
                    titleLabeldesc.font = ghGetFont(handlingSizeForHD(32), "regular")

                    numberEntry = m.botonera.createChild("GHInput")
                    numberEntry.id = "userInput"
                    numberEntry.placeholder = ghTranslate("MDP_Alta_SingleInput_Input_Texto_" + checkoutFieldType, "")
                    numberEntry.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(540)]
                    numberEntry.color = "0xFFFFFF"
                    numberEntry.selColor = "#7F8086"
                    numberEntry.focusColor = "0xFFFFFF"
                    numberEntry.placeholdercolor = "#7F8086"
                    numberEntry.password = "false"
                    numberEntry.width = 504
                    numberEntry.height = 72
                    numberEntry.title = "Inicia sesión"
                    numberEntry.titleColor = "0xFFFFFF"
                    numberEntry.message = "¿Cuál es tu correo electrónico?"
                    numberEntry.messageColor = "0xFFFFFF"
                    numberEntry.keyboardType = "custom"

                    submitButton = m.botonera.createChild("GHButton")
                    submitButton.value = "fixedLineSubmitEcuador"
                    submitButton.id = "submitButton"
                    submitButton.text = ghTranslate("MDP_Formulario_Singleinput_TextoBotonPrimario_" + checkoutFieldType, "")
                    submitButton.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(660)]
                    submitButton.color = "0xFFFFFF"
                    submitButton.selColor = "#FFFFFF"
                    submitButton.width = 504
                    submitButton.height = 72
                    submitButton.backcolor = "#981C15"
                    submitButton.selBackColor = "#981C15"
                    submitButton.focusColor = "0xFFFFFF"
                    submitButton.disableButton = true

                    cancelButton = m.botonera.createChild("GHButton")
                    cancelButton.value = "Cancel"
                    cancelButton.id = "cancelButton"
                    cancelButton.text = ghTranslate("MDP_Formulario_DoubleOption_TextoBotonSecundario_" + checkoutFieldType, "")
                    cancelButton.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(772.5)]
                    cancelButton.color = "0xFFFFFF"
                    cancelButton.selColor = "#FFFFFF"
                    cancelButton.width = 504
                    cancelButton.height = 72
                    cancelButton.backcolor = "#2E303D"
                    cancelButton.selBackColor = "#2E303D"
                    cancelButton.focusColor = "0xFFFFFF"
                    focusMappingHandler(checkoutFieldType)

                end if
            end if

        else if checkoutFieldType = "telmexmexicogate"
            m.GHCustomKeyboard.visible = "false"
            m.logo.setFields({ "uri": ghGetAsset("MDP_Telmex_Alerta_Icono", ""), "height": handlingSizeForHD(105), "width": handlingSizeForHD(105), "translation": [handlingSizeForHD(909), handlingSizeForHD(289.5)] })
            telmexLabel = m.pagemetaData.createChild("Label")
            telmexLabel.setFields({ "text": ghTranslate("MDP_Telmex_Alerta_Texto1", "") + " " + ghTranslate("MDP_Telmex_Alerta_Texto2", "") + " " + ghTranslate("MDP_Telmex_Alerta_Texto3", ""), "color": "0xFFFFFF", "font": ghGetFont(handlingSizeForHD(42), "regular"), "width": handlingSizeForHD(1188), "horizAlign": "center", "wrap": true })
            m.pagemetaData.translation = [handlingSizeForHD(381), handlingSizeForHD(468)]

            submitButton = m.botonera.createChild("GHButton")
            submitButton.value = "telmexSubmit"
            submitButton.id = "telmexSubmit"
            submitButton.text = ghTranslate("MDP_Telmex_Alerta_TextoBotonPrimario", "")
            submitButton.translation = [handlingSizeForHD(702), handlingSizeForHD(738)]
            submitButton.color = "0xFFFFFF"
            submitButton.selColor = "#FFFFFF"
            submitButton.width = handlingSizeForHD(539)
            submitButton.height = handlingSizeForHD(96)
            submitButton.backcolor = "#981C15"
            submitButton.selBackColor = "#981C15"
            submitButton.focusColor = "0xFFFFFF"
            submitButton.disableButton = true

        else if checkoutFieldType = "claropagosgate" and m.top.screenType <> 100
            titleLabel = m.pagemetaData.createChild("Label")
            titleLabel.text = ghTranslate("MDP_AgregarTarjetaDatos_TextoTitulo", "")
            m.pagemetaData.translation = [handlingSizeForHD(1028), handlingSizeForHD(251)]
            titleLabel.width = handlingSizeForHD(734)
            titleLabel.height = handlingSizeForHD(48)
            titleLabel.color = "0xFFFFFF"
            titleLabel.font = ghGetFont(handlingSizeForHD(41), "regular")

            cardNumberEntry = m.botonera.createChild("GHInput")
            cardNumberEntry.id = "cardNumberInput"
            cardNumberEntry.placeholder = ghTranslate("MDP_AgregarTarjeta_Input_Nombre_Texto", "")
            cardNumberEntry.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(388.5)]
            cardNumberEntry.color = "0xFFFFFF"
            cardNumberEntry.selColor = "#7F8086"
            cardNumberEntry.focusColor = "0xFFFFFF"
            cardNumberEntry.placeholdercolor = "#7F8086"
            cardNumberEntry.password = "false"
            cardNumberEntry.width = 504
            cardNumberEntry.height = 72
            cardNumberEntry.keyboardType = "custom"

            userNameEntry = m.botonera.createChild("GHInput")
            userNameEntry.id = "userNameInput"
            userNameEntry.placeholder = ghTranslate("MDP_AgregarTarjeta_Input_NoTarjeta_Texto", "")
            userNameEntry.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(492)]
            userNameEntry.color = "0xFFFFFF"
            userNameEntry.selColor = "#7F8086"
            userNameEntry.focusColor = "0xFFFFFF"
            userNameEntry.placeholdercolor = "#7F8086"
            userNameEntry.password = "false"
            userNameEntry.width = 504
            userNameEntry.height = 72
            userNameEntry.keyboardType = "custom"

            monthEntry = m.botonera.createChild("GHInput")
            monthEntry.id = "monthButton"
            monthEntry.placeholder = ghTranslate("MDP_AgregarTarjeta_Input_Fecha_Texto", "")
            monthEntry.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(594)]
            monthEntry.color = "0xFFFFFF"
            monthEntry.selColor = "#7F8086"
            monthEntry.focusColor = "0xFFFFFF"
            monthEntry.placeholdercolor = "#7F8086"
            monthEntry.password = "false"
            monthEntry.width = 180
            monthEntry.height = 72
            monthEntry.keyboardType = "none"
            monthEntry.dropDownIconVisibility = true

            yearEntry = m.botonera.createChild("GHInput")
            yearEntry.id = "yearButton"
            yearEntry.placeholder = ghTranslate("MDP_AgregarTarjeta_Input_Anio_Texto", "")
            yearEntry.translation = [handlingSizeForHD(1270), handlingSizeForHD(594)]
            yearEntry.color = "0xFFFFFF"
            yearEntry.selColor = "#7F8086"
            yearEntry.focusColor = "0xFFFFFF"
            yearEntry.placeholdercolor = "#7F8086"
            yearEntry.password = "false"
            yearEntry.width = 180
            yearEntry.height = 72
            yearEntry.keyboardType = "none"
            yearEntry.dropDownIconVisibility = true

            cvvEntry = m.botonera.createChild("GHInput")
            cvvEntry.id = "cvvInput"
            cvvEntry.placeholder = ghTranslate("MDP_AgregarTarjeta_Input_CVV_Texto", "")
            cvvEntry.translation = [handlingSizeForHD(1515), handlingSizeForHD(594)]
            cvvEntry.color = "0xFFFFFF"
            cvvEntry.selColor = "#7F8086"
            cvvEntry.focusColor = "0xFFFFFF"
            cvvEntry.placeholdercolor = "#7F8086"
            cvvEntry.password = "true"
            cvvEntry.width = 180
            cvvEntry.height = 72
            cvvEntry.keyboardType = "custom"

            submitButton = m.botonera.createChild("GHButton")
            submitButton.setFields({ id: "submitButton", value: "creditCardDetail1Submit", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#981C15", selBackColor: "#981C15", focusColor: "0xFFFFFF", disableButton: true })
            submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(837)]
            submitButton.text = ghTranslate("MDP_AgregarTarjeta_TextoBotonPrimario", "")

            cancelButton = m.botonera.createChild("GHButton")
            cancelButton.setFields({ id: "cancelButton", value: "BACK", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
            cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(933)]
            cancelButton.text = ghTranslate("MDP_AgregarTarjeta_TextoBotonSecundario", "")

            focusMappingHandler(checkoutFieldType)

        else if checkoutFieldType = "claropagosgate" and m.top.screenType = 100

            titleLabel = m.pagemetaData.createChild("Label")
            titleLabel.text = ghTranslate("MDP_AgregarTarjetaDatos_TextoTitulo", "")
            m.pagemetaData.translation = [handlingSizeForHD(1028), handlingSizeForHD(251)]
            titleLabel.width = handlingSizeForHD(734)
            titleLabel.height = handlingSizeForHD(48)
            titleLabel.color = "0xFFFFFF"
            titleLabel.font = ghGetFont(handlingSizeForHD(41), "regular")

            addressInputEntry = m.botonera.createChild("GHInput")
            addressInputEntry.id = "addressInput"
            addressInputEntry.placeholder = ghTranslate("MDP_AgregarTarjetaDatos_Input_Direccion_Texto", "")
            addressInputEntry.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(388.5)]
            addressInputEntry.color = "0xFFFFFF"
            addressInputEntry.selColor = "#7F8086"
            addressInputEntry.focusColor = "0xFFFFFF"
            addressInputEntry.placeholdercolor = "#7F8086"
            addressInputEntry.password = "false"
            addressInputEntry.width = 504
            addressInputEntry.height = 72
            addressInputEntry.keyboardType = "custom"

            cityInputEntry = m.botonera.createChild("GHInput")
            cityInputEntry.id = "cityInput"
            cityInputEntry.placeholder = ghTranslate("MDP_AgregarTarjetaDatos_Input_Ciudad_Texto", "")
            cityInputEntry.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(492)]
            cityInputEntry.color = "0xFFFFFF"
            cityInputEntry.selColor = "#7F8086"
            cityInputEntry.focusColor = "0xFFFFFF"
            cityInputEntry.placeholdercolor = "#7F8086"
            cityInputEntry.password = "false"
            cityInputEntry.width = 504
            cityInputEntry.height = 72
            cityInputEntry.keyboardType = "custom"

            estadoButtonEntry = m.botonera.createChild("GHInput")
            estadoButtonEntry.id = "estadoButton"
            estadoButtonEntry.placeholder = ghTranslate("MDP_AgregarTarjetaDatos_Input_Estado_Texto", "")
            estadoButtonEntry.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(595.5)]
            estadoButtonEntry.color = "0xFFFFFF"
            estadoButtonEntry.selColor = "#7F8086"
            estadoButtonEntry.focusColor = "0xFFFFFF"
            estadoButtonEntry.placeholdercolor = "#7F8086"
            estadoButtonEntry.password = "false"
            estadoButtonEntry.width = 255
            estadoButtonEntry.height = 72
            estadoButtonEntry.keyboardType = "none"
            estadoButtonEntry.dropDownIconVisibility = true

            cpInputEntry = m.botonera.createChild("GHInput")
            cpInputEntry.id = "cpInput"
            cpInputEntry.placeholder = ghTranslate("MDP_AgregarTarjetaDatos_Input_CP_Texto", "")
            cpInputEntry.translation = [handlingSizeForHD(1401), handlingSizeForHD(594)]
            cpInputEntry.color = "0xFFFFFF"
            cpInputEntry.selColor = "#7F8086"
            cpInputEntry.focusColor = "0xFFFFFF"
            cpInputEntry.placeholdercolor = "#7F8086"
            cpInputEntry.password = "false"
            cpInputEntry.width = 255
            cpInputEntry.height = 72
            cpInputEntry.keyboardType = "custom"

            phoneNumberInputEntry = m.botonera.createChild("GHInput")
            phoneNumberInputEntry.id = "phoneNumberInput"
            phoneNumberInputEntry.placeholder = ghTranslate("MDP_AgregarTarjetaDatos_Input_Telefono_Texto", "")
            phoneNumberInputEntry.translation = [handlingSizeForHD(1027.5), handlingSizeForHD(699)]
            phoneNumberInputEntry.color = "0xFFFFFF"
            phoneNumberInputEntry.selColor = "#7F8086"
            phoneNumberInputEntry.focusColor = "0xFFFFFF"
            phoneNumberInputEntry.placeholdercolor = "#7F8086"
            phoneNumberInputEntry.password = "false"
            phoneNumberInputEntry.width = 504
            phoneNumberInputEntry.height = 72
            phoneNumberInputEntry.keyboardType = "custom"

            submitButton = m.botonera.createChild("GHButton")
            submitButton.setFields({ id: "submitButton", value: "creditCardDetail2Submit", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#981C15", selBackColor: "#981C15", focusColor: "0xFFFFFF", disableButton: true })
            submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(837)]
            submitButton.text = ghTranslate("MDP_AgregarTarjeta_TextoBotonPrimario", "")

            cancelButton = m.botonera.createChild("GHButton")
            cancelButton.setFields({ id: "cancelButton", value: "BACK", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
            cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(933)]
            cancelButton.text = ghTranslate("MDP_AgregarTarjeta_TextoBotonSecundario", "")

            focusMappingHandler(checkoutFieldType)

        end if
    end if
end sub

sub customGrid(data)
    multiLineGrid = CreateObject("roSGNode", "CustomMarkGrid")
    multiLineGrid.ObserveField("selected", "onMultiLineSelected")
    multiLineGrid.id = "multiLineGrid"
    m.top.routerChild = {
        page: multiLineGrid,
        fields: {
            gridType: "multiLineGrid"
            gridData: data
        }
    }
end sub

sub focusMappingHandler(checkoutFieldType)
    screenType = m.top.screenType
    submitButton = m.botonera.findNode("submitButton")
    if checkoutFieldType = "hubfacturafijagate"
        if m.multiLinesAvailable then
            if m.region = "guatemala" or m.region = "nicaragua" or m.region = "chile" or m.region = "argentina" or m.region = "costarica"
                if submitButton.disableButton = true
                    m.botonera.map = {
                        "fixedLineNumberInput": { "up": invalid, "right": invalid, "down": "cancelButton", "left": invalid },
                        ' "submitButton": { "up": "fixedLineNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "fixedLineNumberInput", "right": invalid, "down": invalid, "left": invalid },
                    }
                else
                    m.botonera.map = {
                        "fixedLineNumberInput": { "up": invalid, "right": invalid, "down": "submitButton", "left": invalid },
                        "submitButton": { "up": "fixedLineNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                    }
                end if
            end if
        else if m.singleandmultiLine then
            if m.region = "guatemala" or m.region = "nicaragua" or m.region = "chile" or m.region = "argentina" or m.region = "costarica" or m.region = "honduras" or m.region = "peru" or m.region = "elsalvador" or m.region = "panama"
                if submitButton.disableButton = true
                    m.botonera.map = {
                        "fixedLineNumberInput12": { "up": invalid, "right": invalid, "down": "cancelButton", "left": invalid },
                        ' "submitButton": { "up": "fixedLineNumberInput12", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "fixedLineNumberInput12", "right": invalid, "down": invalid, "left": invalid },
                    }
                else
                    m.botonera.map = {
                        "fixedLineNumberInput12": { "up": invalid, "right": invalid, "down": "submitButton", "left": invalid },
                        "submitButton": { "up": "fixedLineNumberInput12", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                    }
                end if
            else if m.region = "dominicana" then
                if submitButton.disableButton = true

                    m.botonera.map = {
                        "fixedLineNumberInputDomnicana": { "up": invalid, "right": invalid, "down": "fixedLineNumberInputDomnicana12", "left": invalid },
                        "fixedLineNumberInputDomnicana12": { "up": "fixedLineNumberInputDomnicana", "right": invalid, "down": "cancelButton", "left": invalid },
                        ' "submitButton": { "up": "fixedLineNumberInput1", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "fixedLineNumberInputDomnicana12", "right": invalid, "down": invalid, "left": invalid },
                    }
                else
                    m.botonera.map = {
                        "fixedLineNumberInputDomnicana": { "up": invalid, "right": invalid, "down": "fixedLineNumberInputDomnicana12", "left": invalid },
                        "fixedLineNumberInputDomnicana12": { "up": "fixedLineNumberInputDomnicana", "right": invalid, "down": "submitButton", "left": invalid },
                        "submitButton": { "up": "fixedLineNumberInputDomnicana12", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                    }
                end if
            else if m.region = "colombia" then
                if submitButton.disableButton = true

                    m.botonera.map = {
                        "fieldDocumentInput": { "up": invalid, "right": invalid, "down": "fieldDocumentNumberInput", "left": invalid },
                        "fieldDocumentNumberInput": { "up": "fieldDocumentInput", "right": invalid, "down": "fieldFixedNumberInput", "left": invalid },
                        "fieldFixedNumberInput": { "up": "fieldDocumentNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        ' "submitButton": { "up": "fieldFixedNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "fieldFixedNumberInput", "right": invalid, "down": invalid, "left": invalid },
                    }
                else
                    m.botonera.map = {
                        "fieldDocumentInput": { "up": invalid, "right": invalid, "down": "fieldDocumentNumberInput", "left": invalid },
                        "fieldDocumentNumberInput": { "up": "fieldDocumentInput", "right": invalid, "down": "fieldFixedNumberInput", "left": invalid },
                        "fieldFixedNumberInput": { "up": "fieldDocumentNumberInput", "right": invalid, "down": "submitButton", "left": invalid },
                        "submitButton": { "up": "fieldFixedNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                    }
                end if
            else if m.region = "ecuador" then
                if submitButton.disableButton = true

                    m.botonera.map = {
                        "userInput": { "up": invalid, "right": invalid, "down": "cancelButton", "left": invalid },
                        ' "fixedLineSubmitEcuador": { "up": "userInput", "right": invalid, "down": "Cancelar", "left": invalid },
                        "cancelButton": { "up": "userInput", "right": invalid, "down": invalid, "left": invalid },
                    }
                else
                    m.botonera.map = {
                        "userInput": { "up": invalid, "right": invalid, "down": "submitButton", "left": invalid },
                        "submitButton": { "up": "userInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                    }
                end if
            end if
        end if
    else if checkoutFieldType = "telcel"
        if screenType = 1
            if submitButton.disableButton = true

                m.botonera.map = {
                    "fieldMobileNumber": { "up": invalid, "right": invalid, "down": "cancelButton", "left": invalid },
                    ' "submitButton": { "up": "fieldMobileNumber", "right": invalid, "down": "cancelButton", "left": invalid },
                    "cancelButton": { "up": "fieldMobileNumber", "right": invalid, "down": invalid, "left": invalid },
                }
            else
                m.botonera.map = {
                    "fieldMobileNumber": { "up": invalid, "right": invalid, "down": "submitButton", "left": invalid },
                    "submitButton": { "up": "fieldMobileNumber", "right": invalid, "down": "cancelButton", "left": invalid },
                    "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                }
            end if
        else

        end if

    else if checkoutFieldType = "claropagosgate" and m.top.screenType <> 100
        if submitButton.disableButton = true
            m.botonera.map = {
                "cardNumberInput": { "up": invalid, "right": invalid, "down": "userNameInput", "left": invalid },
                "userNameInput": { "up": "cardNumberInput", "right": invalid, "down": "monthButton", "left": invalid },
                "monthButton": { "up": "userNameInput", "right": "yearButton", "down": "cancelButton", "left": invalid },
                "yearButton": { "up": "userNameInput", "right": "cvvInput", "down": "cancelButton", "left": "monthButton" },
                "cvvInput": { "up": "userNameInput", "right": invalid, "down": "cancelButton", "left": "yearButton" },
                "cancelButton": { "up": "monthButton", "right": invalid, "down": invalid, "left": invalid },
            }
        else
            m.botonera.map = {
                "cardNumberInput": { "up": invalid, "right": invalid, "down": "userNameInput", "left": invalid },
                "userNameInput": { "up": "cardNumberInput", "right": invalid, "down": "monthButton", "left": invalid },
                "monthButton": { "up": "userNameInput", "right": "yearButton", "down": "submitButton", "left": invalid },
                "yearButton": { "up": "userNameInput", "right": "cvvInput", "down": "submitButton", "left": "monthButton" },
                "cvvInput": { "up": "userNameInput", "right": invalid, "down": "submitButton", "left": "yearButton" },
                "submitButton": { "up": "monthButton", "right": invalid, "down": "cancelButton", "left": invalid },
                "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
            }
        end if

    else if checkoutFieldType = "claropagosgate" and m.top.screenType = 100
        if submitButton.disableButton = true
            m.botonera.map = {
                "addressInput": { "up": invalid, "right": invalid, "down": "cityInput", "left": invalid },
                "cityInput": { "up": "addressInput", "right": invalid, "down": "estadoButton", "left": invalid },
                "estadoButton": { "up": "cityInput", "right": "cpInput", "down": "phoneNumberInput", "left": invalid },
                "cpInput": { "up": "cityInput", "right": invalid, "down": "phoneNumberInput", "left": "estadoButton" },
                "phoneNumberInput": { "up": "estadoButton", "right": invalid, "down": "cancelButton", "left": invalid },
                "cancelButton": { "up": "phoneNumberInput", "right": invalid, "down": invalid, "left": invalid },
            }
        else
            m.botonera.map = {
                "addressInput": { "up": invalid, "right": invalid, "down": "cityInput", "left": invalid },
                "cityInput": { "up": "addressInput", "right": invalid, "down": "estadoButton", "left": invalid },
                "estadoButton": { "up": "cityInput", "right": "cpInput", "down": "phoneNumberInput", "left": invalid },
                "cpInput": { "up": "cityInput", "right": invalid, "down": "phoneNumberInput", "left": "estadoButton" },
                "phoneNumberInput": { "up": "estadoButton", "right": invalid, "down": "submitButton", "left": invalid },
                "submitButton": { "up": "phoneNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
            }
        end if

    else if checkoutFieldType = "telmexmexicogate"

    else if checkoutFieldType = "promogate"
        if submitButton.disableButton = true

            m.botonera.map = {
                "promoCodeInput": { "up": invalid, "right": invalid, "down": "cancel", "left": invalid },
                ' "promogateSubmit": { "up": "promoCodeInput", "right": invalid, "down": "cancel", "left": invalid }
                "cancel": { "up": "promoCodeInput", "right": invalid, "down": invalid, "left": invalid }
            }
        else
            m.botonera.map = {
                "promoCodeInput": { "up": invalid, "right": invalid, "down": "submitButton", "left": invalid },
                "submitButton": { "up": "promoCodeInput", "right": invalid, "down": "cancel", "left": invalid }
                "cancel": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid }
            }
        end if
    else if checkoutFieldType = "hubgate"

        if m.region = "mexico"
            if screenType = 1
                if submitButton.disableButton = true

                    m.botonera.map = {
                        "fieldMobileNumberInput": { "up": invalid, "right": invalid, "down": "cancelButton", "left": invalid },
                        ' "submitButton": { "up": "fieldMobileNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "fieldMobileNumberInput", "right": invalid, "down": invalid, "left": invalid },
                    }
                else
                    m.botonera.map = {
                        "fieldMobileNumberInput": { "up": invalid, "right": invalid, "down": "submitButton", "left": invalid },
                        "submitButton": { "up": "fieldMobileNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                    }
                end if
            else
                if submitButton.disableButton = true

                    m.botonera.map = {
                        "fieldPinInput": { "up": invalid, "right": "submitPin", "down": "cancelButton", "left": invalid },
                        "submitPin": { "up": invalid, "right": invalid, "down": "cancelButton", "left": "fieldPinInput" },
                        ' "submitButton": { "up": "fieldPinInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "fieldPinInput", "right": invalid, "down": invalid, "left": invalid },
                    }
                else
                    m.botonera.map = {
                        "fieldPinInput": { "up": invalid, "right": "submitPin", "down": "submitButton", "left": invalid },
                        "submitPin": { "up": invalid, "right": invalid, "down": "submitButton", "left": "fieldPinInput" },
                        "submitButton": { "up": "fieldPinInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                    }
                end if
            end if
        else if m.region = "colombia"
            if submitButton.disableButton = true

                m.botonera.map = {
                    "fieldDocumentInput": { "up": invalid, "right": invalid, "down": "fieldDocumentNumberInput", "left": invalid },
                    "fieldDocumentNumberInput": { "up": "fieldDocumentInput", "right": invalid, "down": "fieldFixedNumberInput", "left": invalid },
                    "fieldFixedNumberInput": { "up": "fieldDocumentNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                    ' "submitButton": { "up": "fieldFixedNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                    "cancelButton": { "up": "fieldFixedNumberInput", "right": invalid, "down": invalid, "left": invalid },
                }
            else
                m.botonera.map = {
                    "fieldDocumentInput": { "up": invalid, "right": invalid, "down": "fieldDocumentNumberInput", "left": invalid },
                    "fieldDocumentNumberInput": { "up": "fieldDocumentInput", "right": invalid, "down": "fieldFixedNumberInput", "left": invalid },
                    "fieldFixedNumberInput": { "up": "fieldDocumentNumberInput", "right": invalid, "down": "submitButton", "left": invalid },
                    "submitButton": { "up": "fieldFixedNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                    "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                }
            end if
        else if m.region = "argentina" or m.region = "peru"
            if screenType = 1
                if submitButton.disableButton = true

                    m.botonera.map = {
                        "fieldMobileNumberInput": { "up": invalid, "right": invalid, "down": "cancelButton", "left": invalid }
                        ' "submitButton": { "up": "fieldMobileNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "fieldMobileNumberInput", "right": invalid, "down": invalid, "left": invalid },
                    }
                else
                    m.botonera.map = {
                        "fieldMobileNumberInput": { "up": invalid, "right": invalid, "down": "submitButton", "left": invalid },
                        "submitButton": { "up": "fieldMobileNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                    }

                end if
            else
                if submitButton.disableButton = true
                    m.botonera.map = {
                        "fieldPinInput": { "up": invalid, "right": "submitPin", "down": "cancelButton", "left": invalid },
                        "submitPin": { "up": invalid, "right": invalid, "down": "cancelButton", "left": "fieldPinInput" },
                        ' "submitButton": { "up": "fieldPinInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "fieldPinInput", "right": invalid, "down": invalid, "left": invalid },
                    }
                else
                    m.botonera.map = {
                        "fieldPinInput": { "up": invalid, "right": "submitPin", "down": "submitButton", "left": invalid },
                        "submitPin": { "up": invalid, "right": invalid, "down": "submitButton", "left": "fieldPinInput" },
                        "submitButton": { "up": "fieldPinInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                    }
                end if
            end if
        else
            print "OTHER REGION SETUP GRID" m.screenType
            if screenType = 1
                if submitButton.disableButton = true

                    m.botonera.map = {
                        "fieldMovilIDInput": { "up": invalid, "right": invalid, "down": "fieldMobileNumberInput", "left": invalid },
                        "fieldMobileNumberInput": { "up": "fieldMovilIDInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        ' "submitButton": { "up": "fieldMobileNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "fieldMobileNumberInput", "right": invalid, "down": invalid, "left": invalid },
                    }
                else
                    m.botonera.map = {
                        "fieldMovilIDInput": { "up": invalid, "right": invalid, "down": "fieldMobileNumberInput", "left": invalid },
                        "fieldMobileNumberInput": { "up": "fieldMovilIDInput", "right": invalid, "down": "submitButton", "left": invalid },
                        "submitButton": { "up": "fieldMobileNumberInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                    }
                end if
            else
                print "OTHER REGION SETUP GRID " m.screenType
                if submitButton.disableButton = true

                    m.botonera.map = {
                        "fieldPinInput": { "up": invalid, "right": "submitPin", "down": "cancelButton", "left": invalid },
                        "submitPin": { "up": invalid, "right": invalid, "down": "cancelButton", "left": "fieldPinInput" },
                        ' "submitButton": { "up": "fieldPinInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "fieldPinInput", "right": invalid, "down": invalid, "left": invalid },
                    }
                else
                    m.botonera.map = {
                        "fieldPinInput": { "up": invalid, "right": "submitPin", "down": "submitButton", "left": invalid },
                        "submitPin": { "up": invalid, "right": invalid, "down": "submitButton", "left": "fieldPinInput" },
                        "submitButton": { "up": "fieldPinInput", "right": invalid, "down": "cancelButton", "left": invalid },
                        "cancelButton": { "up": "submitButton", "right": invalid, "down": invalid, "left": invalid },
                    }
                end if
            end if
        end if
    end if

end sub


sub onMultiLineSelected(event)
    data = event.getData()
    m.botonera.findNode("multiLineDropDown").value = data
    m.previousFocusedField = "multiLineDropDown"
end sub

sub onWasShown(event)
    data = event.getData()
    if data then
        if m.previousFocusedField <> invalid
            turnFocusTo(m.previousFocusedField)
        else
            turnFocusTo("botonera")
        end if
    end if
end sub

function onKeyEvent(key, press) as boolean
    handled = false
    if press then
        if key = "left"
            buttons = m.botonera
            tot = m.botonera.getChildCount()
            if tot > 0
                for i = 0 to tot - 1
                    if buttons.getChild(i) <> invalid and buttons.getChild(i).id = buttons.valueFocused and Lcase(buttons.getChild(i).id).instr("input") >= 0 and buttons.getChild(i).keyboardType = "custom"
                        button = buttons.findNode(buttons.valueFocused)
                        buttons.getChild(i).focus = false
                        m.GHCustomKeyboard.setFocus(true)
                        m.GHCustomKeyboard.callFunc("updateFieldFocus")
                        handled = true
                        exit for
                    end if
                end for
            end if
        end if
    end if
    return handled
end function

function initialFocus(value as dynamic)
    turnFocusTo("botonera")
end function

sub updateBuyData(event)
    data = event.getData()
    print "CheckoutFieldsEntry updateBuyData:", data

    ' Store the buyData in the viewVOD component so it can be passed to TicketScreen
    if m.viewVOD <> invalid then
        m.viewVOD.data = data
        m.viewVOD.visible = false ' Keep it hidden as it's just for data storage
    end if
end sub

sub NavigateToTicketScreen(fixedLineNumber = invalid)

    hiddenConfirmTransConfig = false
    if m.viewVOD.data <> invalid then
        hiddenConfirmTransConfig = ghGetChild(m.viewVOD.data, "hidden_confirm_trans_config", false)
    end if

    if hiddenConfirmTransConfig then
        print "coming back to vacrs"
        ' Don't show ticket screen, navigate back to vcard instead
        BackTo()
        m.top.routerClose = true
        return
    end if

    ticketScreen = CreateObject("roSGNode", "TicketScreen")
    ticketScreen.id = "TicketScreen"
    ticketScreen.ObserveField("wasClosed", "handleTicketScreenClose")

    ' Pass current buy data to the ticket screen if available
    if m.viewVOD.data <> invalid then
        ' Add the fixed line number to the buyData
        buyDataWithFixedLine = m.viewVOD.data
        if fixedLineNumber <> invalid then
            buyDataWithFixedLine.fixedLineNumber = fixedLineNumber
        end if
        ticketScreen.buyData = buyDataWithFixedLine
    end if

    ' Navigate to the ticket screen using router
    m.top.routerChild = {
        page: ticketScreen
    }
end sub

sub handleTicketScreenClose(event)
    scr = event.getRoSGNode()
    print "CheckoutFieldsEntry handleTicketScreenClose:", scr.value

    ' Handle TicketScreen closure - navigate back to Vcard when cancel is clicked
    if scr.value <> invalid and scr.value.option <> invalid then
        if scr.value.option = "CERRAR" then
            ' Cancel button clicked - exit entire buy flow and return to Vcard
            print "TicketScreen cancel button clicked, exiting buy flow and returning to Vcard"
            ' Close CheckoutFieldsEntry and signal to close the entire buy flow
            m.top.value = {
                opcion: "CERRAR"
                data: ""
            }
            m.top.close = true
        else if scr.value.option = "BACK" then
            ' Back button clicked - go back one step
            print "TicketScreen back button clicked, going back one step"
            m.top.value = {
                opcion: "BACK"
                data: ""
            }
            m.top.close = true
        else if scr.value.option = "ACCEPT" then
            ' Accept button clicked - could handle differently if needed
            print "TicketScreen accept button clicked, returning to Vcard"
            m.top.value = {
                opcion: "CERRAR"
                data: ""
            }
            m.top.close = true
        end if
    else
        ' Default case - return to Vcard
        print "TicketScreen closed without specific option, returning to Vcard"
        m.top.value = {
            opcion: "CERRAR"
            data: ""
        }
        m.top.close = true
    end if
end sub

function fetchButtonChild(valueFocused as string)
    buttons = m.botonera
    tot = m.botonera.getChildCount()
    if tot > 0
        for i = 0 to tot - 1
            if buttons.getChild(i) <> invalid and buttons.getChild(i).id = valueFocused
                return buttons.getChild(i)
                exit for
            end if
        end for
    end if
    return invalid
end function
