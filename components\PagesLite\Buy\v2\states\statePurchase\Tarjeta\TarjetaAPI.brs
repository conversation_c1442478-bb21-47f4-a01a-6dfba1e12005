sub DataInit()
    if m.top.apiCallType = "addUserForTarjeta"
        addUserForTarjeta()
    else if m.top.apiCallType = "checkUserPartTarjeta"
        checkUserPartTarjeta()
    else if m.top.apiCallType = "checkAvailableTarjeta"
        checkAvailableTarjeta()
    else if m.top.apiCallType = "addNewTarjeta"
        addNewTarjeta()
    end if
    m.api.headers.addReplace("Content-Type", "application/json")
    m.api.headers.addReplace("Authorization", "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")'ghGetRegistry("user_token", "user"))
end sub

sub checkUserPartTarjeta()
    'm.api.url = m.config.mfwk.host + "/cliente/email/"+ghGetRegistry("user_email", "user")
    m.api.url = "https://api.sandbox.claropagos.com/v1" + "/cliente/email/" + "<EMAIL>"'ghGetRegistry("user_email", "user")
end sub

sub addUserForTarjeta()
    ' m.api.url = m.config.mfwk.host + "/cliente"
    ' m.api.url = "https://api.claropagos.com/v1" '+ "/cliente"
    m.api.url = "https://api.sandbox.claropagos.com/v1/cliente"
    m.api.method = "POST"
    m.api.timeout = 30000
    m.api.body = formatJSON(m.top.postBody)
end sub

sub checkAvailableTarjeta()
    'm.api.url = m.config.mfwk.host + "/cliente/"+ghGetRegistry("user_id", "user")+ "/tarjeta"
    m.api.url = "https://api.sandbox.claropagos.com/v1" + "/cliente/" + m.top.client_id + "/tarjeta"
end sub

sub addNewTarjeta()
    m.api.url = "https://api.sandbox.claropagos.com/v1/tarjeta"
    m.api.method = "POST"
    m.api.timeout = 30000
    m.api.body = formatJSON(m.top.postBody)
end sub

sub ProcessData(res, raw)
    response = ghGetChild(res, "data")
    ?"response==>"response
    if res = invalid
        m.top.error = ghErrorNetwork(m.api.name, m.api.url, raw)
        return
    end if

    'errors = ghGetChild(res, "errors")
    if response.errors <> invalid then
        m.top.error = res
        return
    end if

    m.top.content = response
end sub