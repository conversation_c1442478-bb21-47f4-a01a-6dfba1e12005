<component name="CheckoutFieldsEntry" extends="Page">
	<script type="text/brightscript" uri="CheckoutFieldsEntry.brs" />
	<script type="text/brightscript" uri="HubGateEntryFields.brs" />
	<script type="text/brightscript" uri="pkg:/source/GHRDK/Files.brs" />
	<script type="text/brightscript" uri="CreditCardEntryFields.brs" />
	<script type="text/brightscript" uri="TarjetaAPIResponseHandler.brs" />

	<interface>
		<!-- INTERFAZ DE ENTRADA -->
		<field id="accessCode" type="assocarray" />
		<field id="value" type="assocarray" />
		<field id="data" type="assocarray" />
		<field id="cardDetails" type="assocarray" />
		<field id="selected" type="node" />
		<field id="screenType" type="int" onChange="onScreenTypeChange" />
		<field id="checkoutFieldType" type="string" onChange="handleRightSectionLayout" />
		<field id="buyData" type="assocarray" onChange="updateBuyData" />
		<function name="initialFocus" />
	</interface>
	<children>
		<Poster id="logo" />
		<GHCustomKeyboard id="GHCustomKeyboard" />
		<CheckoutViewVOD id="viewVOD1" visible="false" />
		<LayoutGroup id="pagemetaData" layoutDirection="vert" />
		<GHButtonGroup id="botonera" layout="map" orientation="vertical" handleKey="false">
		</GHButtonGroup>
	</children>
</component>
