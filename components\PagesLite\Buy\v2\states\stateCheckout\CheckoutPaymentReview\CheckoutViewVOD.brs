' CheckoutViewVOD

sub init()
    m.top.debug = true
    m.logger = createLogger()

    ' Constantes para tipos de compra
    m.PRODUCT_TYPE_PREBUY = "CV_PREBUY"
    m.PRODUCT_TYPE_PRE_ESTEXC = "CV_PRE_ESTEXC"
    m.PRODUCT_TYPE_STDBUY = "CV_STDBUY"
    m.PRODUCT_TYPE_DOWNLOAD_BUY = "download_buy"

    ' Constantes para tipos de renta
    m.PRODUCT_TYPE_PRERENT = "CV_PRERENT"
    m.PRODUCT_TYPE_STDRENT = "CV_STDRENT"

    ' Constantes para episodios y temporadas
    m.PRODUCT_TYPE_EPISODEBUY = "CV_EPISODEBUY"
    m.PRODUCT_TYPE_SEASONBUY = "CV_SEASONBUY"

    ' Constantes para suscripciones
    m.ADDON_TYPE_SUBSCRIPTION = "subscrition"
end sub

sub updateData(event)
    data = event.getData()

    m.logger.debug("updateData", { data: data })

    ' Extraer datos del evento
    productData = extractProductData(data)

    ' Configurar elementos comunes de UI
    setupCommonUI(productData)

    ' Aplicar configuraciones específicas según tipo de producto
    if productData.isAddon = m.ADDON_TYPE_SUBSCRIPTION then
        m.logger.debug("isAddon")
        setupSubscriptionUI(productData)

    else
        ' Configurar UI según tipo de producto
        if isProductTypeBuy(productData.buyProductType, productData.isAddon) then
            m.logger.debug("isProductTypeBuy")
            setupBuyUI(productData)

        else if isProductTypeRent(productData.buyProductType) then
            m.logger.debug("isProductTypeRent")
            setupRentUI(productData)

        else if isEpisodeOrSeasonBuy(productData.buyProductType) then
            m.logger.debug("isEpisodeOrSeasonBuy")
            setupEpisodeOrSeasonUI(productData)

        else
            ' Caso por defecto para otros tipos de productos
            m.logger.debug("product default")
            setupDefaultUI(productData)
        end if
    end if
end sub

'**********************************************************************
' Funciones auxiliares para extraer y organizar datos
'**********************************************************************
function extractProductData(data as object) as object
    productData = {
        buyType: ghGetChild(data, "buyType", "compra o renta"),
        buyPrice: ghGetChild(data, "buyPrice", "00.00"),
        buyCurrency: ghGetChild(data, "buyCurrency", "$"),
        buyProductType: ghGetChild(data, "buyProductType"),
        buyPeriodo: ghGetChild(data, "buyPeriodo", ""),
        oneoffertype: ghGetChild(data, "buyIsAddon", invalid),
        isAddon: ghGetChild(data, "buyIsAddon", invalid),
        family: ghGetChild(data, "buyFamily", invalid),
        banner: ghGetChild(data, "buyBanner", ""),

        contentImage: ghGetChild(data, "contentImagen", ""),
        contentTitle: ghGetChild(data, "content_name", ""), ' Series title from "content/serie" API
        contentTitleSeason: ghGetChild(data, "contentTitleSeason", ""), ' Episode info from "workflowstart" API

        ivaText: ghTranslate("doconfirm_iva_description", "IVA incluido")
        user_type: getUserTypeGA4(true),
        country: getCountryCode(ghGetRegistry("country_code", "user")),
        user_id: ghGetRegistry("user_id", "user"),
        screen_name: "content selection",
        screen_class: "/content-selection"
    }

    GA4Event("CheckOut_VOD", productData)

    productData.fullPrice = productData.buyCurrency + productData.buyPrice
    productData.formattedPrice = ghReplaceStr(productData.fullPrice, ".00", " ")

    return productData
end function

'**********************************************************************
' Funciones para verificar tipos de productos
'**********************************************************************
function isProductTypeBuy(buyProductType as string, isAddon as dynamic) as boolean
    return buyProductType = m.PRODUCT_TYPE_PREBUY or buyProductType = m.PRODUCT_TYPE_PRE_ESTEXC or buyProductType = m.PRODUCT_TYPE_STDBUY or isAddon = m.PRODUCT_TYPE_DOWNLOAD_BUY
end function

function isProductTypeRent(buyProductType as string) as boolean
    return buyProductType = m.PRODUCT_TYPE_PRERENT or buyProductType = m.PRODUCT_TYPE_STDRENT
end function

function isEpisodeOrSeasonBuy(buyProductType as string) as boolean
    return buyProductType = m.PRODUCT_TYPE_EPISODEBUY or buyProductType = m.PRODUCT_TYPE_SEASONBUY
end function

'**********************************************************************
' Funciones para configurar la UI
'**********************************************************************
sub setupCommonUI(productData as object)
    ' taxText = ghTranslate("Transaccionales_Checkout_TextoImpuestos_" + producttype, "VAT included")

    producttype = productData.buyProductType
    banner = productData.banner
    region = getCountryCode(ghGetRegistry("country_code", "user"))

    ' region = getCountryCode(ghGetRegistry("country_code", "user"))

    m.top.findNode("logoAddon").uri = ghGetAsset("Transaccionales_Checkout_LogoAddon_" + producttype, "")

    m.top.findNode("movieImage").setFields({
        uri: ghGetAsset("Transaccionales_Checkout_ImagenDeContenido_" + banner + "_" + region, ""),
        ' uri: productData.contentImage,
        height: 240,
        width: 160
    })

    m.top.findNode("iva").setFields({
        font: ghGetFont(12, "regular"),
        text: ghTranslate("Transaccionales_Checkout_TextoImpuestos_" + producttype, "VAT included"),
        ' translation: "[0,20]",
        horizAlign: "center",
        visible: "true",
        color: "#FFFFFF",
        wrap: "false"
    })

    m.top.findNode("textoInformativo").setFields({
        font: ghGetFont(18, "regular"),
        text: ghTranslate("Transaccionales_Checkout_TextoPromocion_" + producttype, ""),
        horizAlign: "left",
        visible: "true",
        color: "#FFFFFF",
        wrap: "false"
    })


    ' Combine all policy texts for rental scenarios
    policies1 = ghTranslate("Transaccionales_Checkout_TextoPoliticas1_" + producttype, "")
    policies2 = ghTranslate("Transaccionales_Checkout_TextoPoliticas2_" + producttype, "")
    policies3 = ghTranslate("Transaccionales_Checkout_TextoPoliticas3_" + producttype, "")
    policies4 = ghTranslate("Transaccionales_Checkout_TextoPoliticas4_" + producttype, "")
    policies5 = ghTranslate("Transaccionales_Checkout_TextoPoliticas5_" + producttype, "")

    combinedPolicies = policies1
    if policies2 <> "" then combinedPolicies = combinedPolicies + " " + policies2
    if policies3 <> "" then combinedPolicies = combinedPolicies + " " + policies3
    if policies4 <> "" then combinedPolicies = combinedPolicies + " " + policies4
    if policies5 <> "" then combinedPolicies = combinedPolicies + " " + policies5

    print "combinedPolicies "combinedPolicies
    m.top.findNode("leyendaInformativa").setFields({
        font: ghGetFont(13, "regular"),
        text: combinedPolicies,
        horizAlign: "left",
        visible: "true",
        wrap: "true",
        lineSpacing: 0,
        width: "339.54",
        height: "220",
        color: "#FFFFFF"
    })

    producttype = productData.buyProductType
    m.top.findNode("oferta").setFields({
        font: ghGetFont(21, "bold"),
        text: ghTranslate("Transaccionales_Checkout_TextoOferta_" + producttype, ""),
        horizAlign: "left"
    })

    m.top.findNode("total").setFields({
        font: ghGetFont(25, "regular"),
        text: ghTranslate("Transaccionales_Checkout_TextoTotal", "Total:"),
        horizAlign: "left"
    })

    m.top.findNode("periodo").setFields({
        font: ghGetFont(17, "regular"),
        text: ghTranslate("Transaccionales_Checkout_TextoTemporalidad_" + productData.buyProductType, ""),
        horizAlign: "center"
    })

    m.top.findNode("title").setFields({
        font: ghGetFont(26, "regular"),
        text: ghTranslate("", "Estás por contratar:"),
        horizAlign: "center"
    })
end sub

sub setupBuyUI(productData as object)
    producttype = productData.buyProductType
    priceText = ghTranslate("Transaccionales_Checkout_TextoPrecio_" + producttype, productData.buyCurrency + productData.buyPrice)
    oneoffertype = productData.oneoffertype

    m.top.findNode("title").setFields({
        font: ghGetFont(32, "medium"),
        text: ghTranslate("Transaccionales_Checkout_TextoTitulo_" + oneoffertype, "Estás por comprar"),
        ' text: ghTranslate("doconfirm_compra_title", "Estás por comprar"),
        horizAlign: "center"
    })

    m.top.findNode("buyPrice").setFields({
        font: ghGetFont(26, "bold"),
        text: priceText,
        ' text: productData.formattedPrice,
        horizAlign: "center"
    })

    m.top.findNode("iva").setFields({ visible: "true" })

    print " iva"m.top.findNode("iva")

    m.top.findNode("textoInformativo").setFields({ visible: "false" })
    m.top.findNode("oferta").setFields({ visible: "false" })

    m.top.findNode("leyendaInformativa").setFields({
        visible: "true",
        color: "#858585"
    })

    m.top.findNode("periodo").setFields({
        font: ghGetFont(17, "regular"),
        text: productData.ivaText
    })
end sub

sub setupRentUI(productData as object)
    producttype = productData.buyProductType
    oneoffertype = productData.oneoffertype

    ' Fix title translation to use actual oneoffertype value instead of placeholder
    m.top.findNode("title").setFields({
        font: ghGetFont(32, "medium"),
        text: ghTranslate("Transaccionales_Checkout_TextoTitulo_" + oneoffertype, "You're Renting"),
        horizAlign: "center"
    })

    text = ghTranslate("Transaccionales_Checkout_TextoTitulo_" + oneoffertype, "You're Renting")

    print "text "text

    m.top.findNode("movieTitle").setFields({
        font: ghGetFont(26, "regular"),
        text: productData.contentTitle,
        translation: "[515,187]",
        horizAlign: "left"
    })
    print "productData.contentTitle"productData.contentTitle


    ' Fix price to show actual currency and price values
    priceText = ghTranslate("Transaccionales_Checkout_TextoPrecio_" + producttype, productData.buyCurrency + productData.buyPrice)
    m.top.findNode("buyPrice").setFields({
        font: ghGetFont(26, "bold"),
        text: priceText,
        horizAlign: "center"
    })

    m.top.findNode("iva").setFields({ visible: "true" })

    m.top.findNode("oferta").setFields({ visible: "false" })
    m.top.findNode("textoInformativo").setFields({ visible: "false" })

    m.top.findNode("leyendaInformativa").setFields({
        visible: "true",
        color: "#858585"
    })

    ' Combine temporality and tax text as required
    temporalityText = ghTranslate("Transaccionales_Checkout_TextoTemporalidad_" + producttype, "/" + productData.buyPeriodo)
    ' taxText = ghTranslate("Transaccionales_Checkout_TextoImpuestos_" + producttype, "VAT included")

    m.top.findNode("periodo").setFields({
        font: ghGetFont(17, "regular"),
        text: temporalityText,
        ' text: temporalityText + " " + taxText,
        horizAlign: "center"
    })
end sub

sub setupEpisodeOrSeasonUI(productData as object)
    producttype = productData.buyProductType
    oneoffertype = productData.oneoffertype

    ' Determinar el título correcto según sea episodio o temporada
    titleText = productData.contentTitle
    if productData.buyProductType = m.PRODUCT_TYPE_SEASONBUY then
        titleText = productData.contentTitleSeason
    end if

    m.top.findNode("movieTitle").setFields({
        font: ghGetFont(40, "bold"),
        text: titleText,
        horizAlign: "center"
    })

    m.top.findNode("title").setFields({
        font: ghGetFont(32, "medium"),
        text: ghTranslate("Transaccionales_Checkout_TextoTitulo_" + oneoffertype, "Estás por comprar"),
        horizAlign: "center"
    })

    priceText = ghTranslate("Transaccionales_Checkout_TextoPrecio_" + producttype, productData.buyCurrency + productData.buyPrice)

    m.top.findNode("buyPrice").setFields({
        font: ghGetFont(24, "regular"),
        text: priceText,
        ' text: productData.formattedPrice,
        horizAlign: "center"
    })
end sub

sub setupSubscriptionUI(productData as object)
    producttype = productData.buyProductType
    banner = productData.banner
    oneoffertype = productData.oneoffertype
    region = getCountryCode(ghGetRegistry("country_code", "user"))

    priceText = ghTranslate("Transaccionales_Checkout_TextoPrecio_" + producttype, productData.buyCurrency + productData.buyPrice)

    m.top.findNode("title").setFields({
        font: ghGetFont(32, "medium"),
        text: ghTranslate("Transaccionales_Checkout_TextoTitulo_" + oneoffertype, "Estás por contratar:"),
        horizAlign: "center"
    })

    m.top.findNode("buyPrice").setFields({
        font: ghGetFont(24, "regular"),
        text: priceText,
        ' text: productData.formattedPrice,
        horizAlign: "center"
    })

    ' m.top.findNode("iva").setFields({
    '     font: ghGetFont(40, "regular"),
    '     text: ghTranslate("Transaccionales_Checkout_TextoImpuestos_" + producttype, "VAT included"),
    '     translation: "[600,600]",
    '     width: "400",
    '     horizAlign: "center",
    '     visible: "true",
    '     color: "#bf1f8fff",
    '     wrap: "false"
    ' })

    ' m.top.findNode("iva").setFields({ visible: "true" })
    print " iva"m.top.findNode("iva")
    print "translation" m.top.findNode("iva").translation


    m.top.findNode("movieImage").setFields({
        uri: ghGetAsset("Transaccionales_Checkout_ImagenDeContenido_" + banner + "_" + region, ""),

        ' uri: ghGetAsset(productData.banner, ""),
        height: 240,
        width: 160
    })

    m.top.findNode("textoInformativo").setFields({
        width: 339.54
    })
end sub

sub setupDefaultUI(productData as object)
    producttype = productData.buyProductType
    priceText = ghTranslate("Transaccionales_Checkout_TextoPrecio_" + producttype, productData.buyCurrency + productData.buyPrice)

    m.top.findNode("title").setFields({
        font: ghGetFont(32, "medium"),
        text: ghTranslate("checkout_subscription_title_label", "Estás por adquirir"),
        horizAlign: "center"
    })

    m.top.findNode("buyPrice").setFields({
        font: ghGetFont(24, "bold"),
        text: priceText,
        ' text: productData.fullPrice + " " + productData.ivaText,
        horizAlign: "center"
    })
end sub
