' BuyConfirm
' ---------------------------

sub DataInit()
  ' ******************************************
  ' PAYWAYCONFIRM
  ' ******************************************

  m.api.url = m.config.mfwk.host + m.top.buylink

  m.api.query.delete("api_version")

  m.api.headers.Append({ "user-token": ghGetRegistry("user_token", "user") })

  data = m.top.data

  print "final data :" FormatJson(data)

  m.api.query.Append(data)
  m.api.query.Append({
    "user_id": ghGetRegistry("user_id", "user")
  })
  m.api.timeout = 30000

  print "m.api :" FormatJson(m.api)

end sub

sub ProcessData(res, raw)
  if m.top.debug then print "*********************************"
  if m.top.debug then print ghLogHead();"body = ";res
  if m.top.debug then print ghLogHead();"body.entry = ";ghGetChild(res, "entry", "! no existe")


  'STATIC VALUE ADD START
  region = ghGetRegistry("region")
  account = ghGetChild(m.top.data, "account")
  pin = ghGetChild(m.top.data, "pin", invalid)

  print "your account number :"account, pin
  if region <> "colombia"
    if pin = invalid and account <> invalid
      if account.Len() = 10
        print "PhoneNumber success logic :" region
        res = ParseJson(ReadAsciiFile("pkg:/components/PagesLite/Buy/v2/states/statePurchase/hubgate/staticJson/hubgate_phone_success.json"))
      else
        if region = "mexico"
          print "PhoneNumber error logic:" region
          res = ParseJson(ReadAsciiFile("pkg:/components/PagesLite/Buy/v2/states/statePurchase/hubgate/staticJson/hubgate_phone_mexico.json"))
        else
          print "PhoneNumber error logic:" region
          res = ParseJson(ReadAsciiFile("pkg:/components/PagesLite/Buy/v2/states/statePurchase/hubgate/staticJson/hubgate_phone_error.json"))
        end if
      end if
    else
      if pin <> invalid and pin.Len() = 5
        print "pin success logic: " region
        res = ParseJson(ReadAsciiFile("pkg:/components/PagesLite/Buy/v2/states/statePurchase/hubgate/staticJson/hubgate_pin_success.json"))
      else
        print "pin error logic :" region
        res = ParseJson(ReadAsciiFile("pkg:/components/PagesLite/Buy/v2/states/statePurchase/hubgate/staticJson/hubgate_pin_error.json"))
      end if
    end if
  else
    print "PRINT COLOMBIA REGION :" region
    if account <> invalid and account.Len() = 10
      print "PhoneNumber success logic :" region
      res = ParseJson(ReadAsciiFile("pkg:/components/PagesLite/Buy/v2/states/statePurchase/hubgate/staticJson/hubgate_phone_success.json"))
    else
      print "PhoneNumber error logic :" region
      res = ParseJson(ReadAsciiFile("pkg:/components/PagesLite/Buy/v2/states/statePurchase/hubgate/staticJson/hubgate_pin_error.json"))
    end if
  end if


  print "PAYWAY CHECK YOUR RESPONSE :" res
  'STATIC VALUE ADD END

  if account.Len() <> 10 or (pin <> invalid and pin.Len() <> 5)
    ' print "YOUR ERRORCHECK YOUR RESPONSE :" res
    errors = ghGetChild(res, "errors")
    if errors <> invalid then
      res.raw = raw
      m.top.error = res
      return
    end if
  end if

  content = ghGetChild(res, "response", {})
  content.msgTitle = "¡Transacción exitosa!"
  content.msgContent = ghDecodeHTML(ghGetChild(content, "msg", "Su transacción ha sido exitosa."))

  updateGlobalArray("lasttouch", {
    purchased: ghGetChild(res, "response.lastTouch")
  })

  m.top.content = content

  if m.top.debug then print "*********************************"
end sub

