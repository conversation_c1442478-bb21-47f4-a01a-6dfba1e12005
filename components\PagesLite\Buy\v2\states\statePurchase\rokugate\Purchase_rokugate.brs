' ROKUGATE

sub PurchaseRokuGate()
  m.logger.debug("Purchase ROKUGATE Init")

  RokuGateRun()
end sub

sub RokuGateRun(newState = invalid, info = {})
  m.logger.debug("Purchase ROKUGATE Run", { newState: newState, info: info })

  setLoading(false)

  if newState = invalid then
    Roku_DoRokuBuy()
  else if newState = "go" then
    setLoading(true)

    data = ghGetChild(m.buy.states, "purchase.paymentMethod")
    apiConfirm = ghCallApi("BuyConfirmLite", "Roku_Return", "Roku_ReturnError", false)
    apiConfirm.setFields({
      link: ghGetChild(data, "data.buylink", ""),
      obj_type: ghGetChild(data, "data.object_type", "")
      access_code: ghGetChild(data, "data.access_code", "")
      buyToken: ghGetChild(data, "data.buyToken")
      extra_params: {
        transaction_id: m.buy.options.rokugate.transaction_id
      }
    })
    apiConfirm.control = "run"

  else if newState = "ok" then
    JumpTo("purchase", "ok")

  else if newState = "back" then
    JumpTo("checkout")

  else if newState = "error" then
    showMessageError(ghGetChild(info, "data", {}))

  else if newState = "missingparameters" then
    JumpTo("purchase", "missingparameters")

  else
    JumpTo("purchase", "fail")
  end if
end sub

sub Roku_DoRokuBuy()
  ' TODO mejorar de donde se obtiene
  idOrden = ghGetChild(m.top, "buyB.product_id")
  orden = ghRokupayBuildOrder(idOrden)

  m.logger.debug("Purchase ROKUGATE DoRokuBuy", { orden: orden })

  m.store = ghRokupayDoOrder(orden, "Roku_DoRokuBuy_Return")
end sub

sub Roku_DoRokuBuy_Return(event)
  data = event.getData()
  status = ghGetChild(data, "status", -20)

  m.logger.debug("Purchase ROKUGATE DoRokuBuy Return", { data: data, status: status })

  if status = 1 then
    operacion = data.getChild(0)

    m.buy.options.rokugate.transaction_id = ghGetChild(operacion, "purchaseId", "no consegui el codigo")

    RokuGateRun("go")

  else if status = 2 then
    RokuGateRun("error")

  else
    RokuGateRun("error")
  end if
end sub

sub Roku_CallApi()
  data = ghGetChild(m.buy.states, "purchase.method.parameters")

  if inStr(1, ghGetChild(data, "link", ""), "/buyconfirm") > 0 then
    apiConfirm = ghCallApi("BuyConfirmLite", "Roku_Return", "Roku_ReturnError", false)
    apiConfirm.setFields(data)
    apiConfirm.control = "run"
  else
    RokuGateRun("missingparameters")
  end if
end sub

sub RokuGate_DoConfirm_Return(event)
  scr = event.getRoSGNode()

  m.logger.debug("Purchase ROKUGATE DoConfirm Return", { opcion: scr.value.opcion })

  if scr.value.opcion = "BACK" or scr.value.opcion = "CANCEL" then
    RokuGateRun("back")
  else if scr.value.opcion = "SELECT" then
    RokuGateRun("go")
  end if
end sub

sub Roku_Return(event)
  data = event.getData()

  m.logger.debug("Purchase ROKUGATE Return", { data: data })

  ' Show ticket screen instead of going directly to success
  RokuGate_TicketScreenLanding(data)
end sub

sub Roku_ReturnError(event)
  data = event.getData()

  m.logger.error("Purchase ROKUGATE Return Error", { data: data })

  RokuGateRun("error", { data: data })
end sub

sub RokuGate_TicketScreenLanding(data)
  print "RokuGate TicketScreenLanding :" data

  ' Check if hidden_confirm_trans_config is set, similar to stateTicket.brs logic
  hiddenConfirmTransConfig = ghGetChild(m.buy.data, "hidden_confirm_trans_config", false)

  if hiddenConfirmTransConfig then
    ' Don't show ticket screen, proceed to success flow instead
    RokuGateRun("ok")
    return
  end if

  ticketInfo = data
  if ticketInfo = invalid then ticketInfo = invalid
  TicketScreenFinal = CreateObject("roSGNode", "TicketScreen")
  TicketScreenFinal.id = "RokuGateTicketScreenFinal"
  TicketScreenFinal.ObserveField("wasClosed", "RokuGate_TicketScreen_Return")

  m.logger.debug("RokuGate TicketScreenFinal", { checkout: m.buy.states["checkout"] })
  print "RokuGate TicketScreenFinal :" m.buy.states["checkout"]

  ' Pass buy data to ticket screen
  TicketScreenFinal.buyData = m.buy.states["checkout"]

  m.top.routerChild = {
    page: TicketScreenFinal
  }
end sub

sub RokuGate_TicketScreen_Return(event)
  scr = event.getRoSGNode()
  print "RokuGate_TicketScreen_Return :" scr.value

  m.logger.debug("RokuGate_TicketScreen_Return", { option: scr.value.option })

  if scr.value.option = "BACK" or scr.value.option = "CANCEL" then
    ' Go back to checkout
    JumpTo("checkout")
  else if scr.value.option = "CERRAR" then
    ' Handle cancel button from TicketScreen - exit buy flow and return to Vcard
    m.logger.debug("RokuGate TicketScreen cancel button clicked, exiting buy flow")
    JumpTo("out", "ok")
  else if scr.value.option = "ACCEPT" or scr.value.option = "SELECT" then
    ' Proceed to success
    RokuGateRun("ok")
  else
    m.logger.error("RokuGate TicketScreen: no se reconoce la seleccion", { option: scr.value.option })
    RokuGateRun("ok")
  end if
end sub