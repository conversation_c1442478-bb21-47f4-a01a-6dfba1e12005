<?xml version="1.0" encoding="utf-8" ?>

<component name="CheckoutPaymentReview" extends="Page">

  <script type="text/brightscript" uri="CheckoutPaymentReview.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="buyData" type="assocarray" onChange="updateBuyData"/>
    <!-- interfaz de salida -->
    <field id="value" type="assocarray" />
  </interface>

  <children>
    <CheckoutViewVOD id="viewVOD" visible="false" />

    <Label id="payMethod" focusable="false" translation="[0,574]" width="1280" height="24" text="*" />

    <!-- Botonera -->
    <GHButtonGroup id="botonera" layout="childs" orientation="vertical">
      <GHButton id="accept" text="ADQUIRIR" value="ACCEPT" translation="[460,500]" color="0xFFFFFF" selColor="#FFFFFF" width="359" height="72" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />
      <GHButton id="select" value="SELECT" translation="[460,500]" color="0xFFFFFF" selColor="#FFFFFF" width="359" height="72" backcolor="#981C15" selBackColor="#981C15" focusColor="0xFFFFFF" />
      <GHButton id="cancel" text="CANCELAR" value="CANCEL" visible="false" translation="[460,600]" color="0xFFFFFF" selColor="#FFFFFF" width="359" height="72" backcolor="#2E303D" selBackColor="#2E303D" focusColor="0xFFFFFF" />
    </GHButtonGroup>
  </children>

</component>