sub callMonthTypeGrid()
    m.monthTypeGrid = CreateObject("roSGNode", "CustomMarkGrid")
    m.monthTypeGrid.ObserveField("selected", "setMonthTypeField")
    m.monthTypeGrid.id = "monthTypeGrid"
    m.top.routerChild = {
        page: m.monthTypeGrid,
        fields: {
            gridType: "monthType"
            gridData: ["Enero", "Febrero", "Marzo", "Abril", "Mayo", "Junio", "Julio", "Agosto", "Septiembre", "Octubre", "Noviembre", "Diciembre"]
        }
    }
end sub

sub callYearTypeGrid()
    yearTypeGrid = CreateObject("roSGNode", "CustomMarkGrid")
    yearTypeGrid.ObserveField("selected", "setYearTypeField")
    yearTypeGrid.id = "yearTypeGrid"
    yearCheck = CreateObject("roDateTime")
    presentyear = yearCheck.GetYear()
    yearArray = []
    for i = 0 to 4
        yearArray.Push(presentyear.toStr())
        presentyear+=1
    end for
    m.top.routerChild = {
        page: yearTypeGrid,
        fields: {
            gridType: "yearType"
            gridData: yearArray
        }
    }
end sub

sub callEstadoButtonTypeGrid()
    estadoTypeGrid = CreateObject("roSGNode", "CustomMarkGrid")
    estadoTypeGrid.ObserveField("selected", "setEstadoTypeField")
    estadoTypeGrid.id = "estadoTypeGrid"
    m.top.routerChild = {
        page: estadoTypeGrid,
        fields: {
            gridType: "estadoType"
            gridData: ["Aguascalientes", "Baja California", "Baja California Sur", "Campeche", "Chihuahua", "Chiapas", "Ciudad de México", "Coahuila", "Coahuila", "Durango", "Guanajuato", "Guerrero", "Hidalgo", "Jalisco", "México", "Michoacán", "Morelos", "Nayarit", "Nuevo León", "Oaxaca", "Puebla", "Querétaro", "Quintana Roo", "San Luis Potosí", "Sinaloa", "Sonora", "Tabasco", "Tamaulipas", "Tlaxcala", "Veracruz", "Yucatán", "Zacatecas"]
        }
    }
end sub

sub setMonthTypeField(event)
    data = event.getData()
    print "setMonthTypeField:" data
    selectedIndex = m.monthTypeGrid.itemSelected + 1
    if selectedIndex < 10
        data = "0" + selectedIndex.toStr()
    else
        data = selectedIndex.toStr()
    end if
    m.top.findNode("monthButton").value = data
end sub

sub setYearTypeField(event)
    data = event.getData()
    print "setYearTypeField:" data
    m.top.findNode("yearButton").value = data.Right(2)
end sub


sub setEstadoTypeField(event)
    data = event.getData()
    print "setEstadoTypeField:" data
    m.top.findNode("estadoButton").value = data
end sub
