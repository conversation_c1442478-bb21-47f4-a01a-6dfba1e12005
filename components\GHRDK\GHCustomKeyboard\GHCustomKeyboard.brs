sub init()
    m.ifNumericSpecialChar = true
    m.ifAlphaNumeric = false
    m.ifCapsAlphaNumeric = false
    m.rowIndex = 0
    m.keyIndex = 0
    m.lastTopIndex = 0
    m.topSectionFocus = false
    handledKeys()
end sub

sub handledKeys()
    m.top.removeChildren(m.top.getChildren(m.top.getChildCount(), 0))
    if m.ifNumericSpecialChar = true
        m.ArNumb = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"]
        m.SpChr1 = ["/", ":", ";", "(", ")", "$", "&", "@", "”", ","]
        m.SpChr2 = ["¿", "?", "¡", "!", "'", "[", "]", "{", "}", "#"]
        m.SpChr3 = ["%", "^", "*", "=", "\", "|", "~", "<", ">", "> "]
        m.SpChr4 = ["_", "-", "spacebar", ".", "+"]

        m.keyRows = [m.ArNumb, m.SpChr1, m.SpChr2, m.SpChr3]
        m.keyActivityRows = [m.ArNumb, m.SpChr1, m.SpChr2, m.SpChr3, m.SpChr4]

    else if m.ifAlphaNumeric = true
        m.ArNumb = ["q", "w", "e", "r", "t", "y", "u", "i", "o", "p"]
        m.SpChr1 = ["a", "s", "d", "f", "g", "h", "j", "k", "l", "ñ"]
        m.SpChr2 = ["caps", "z", "x", "c", "v", "b", "n", "m", "@"]
        m.SpChr3 = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"]
        m.SpChr4 = ["_", "-", "spacebar", ".", "+"]

        m.keyRows = [m.ArNumb, m.SpChr1]
        m.keyActivityRows = [m.ArNumb, m.SpChr1, m.SpChr2, m.SpChr3, m.SpChr4]

    else if m.ifCapsAlphaNumeric = true
        m.ArNumb = ["Q", "W", "E", "R", "T", "Y", "U", "I", "O", "P"]
        m.SpChr1 = ["A", "S", "D", "F", "G", "H", "J", "K", "L", "N"]
        m.SpChr2 = ["caps", "Z", "X", "C", "V", "B", "N", "M", "@"]
        m.SpChr3 = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"]
        m.SpChr4 = ["_", "-", "spacebar", ".", "+"]

        m.keyRows = [m.ArNumb, m.SpChr1]
        m.keyActivityRows = [m.ArNumb, m.SpChr1, m.SpChr2, m.SpChr3, m.SpChr4]

    end if
    createKeyboardDesign(m.keyRows)
end sub

sub createKeyboardDesign(keyRows as object)

    focusRing = m.top.createChild("Rectangle")
    focusRing.id = "focusRing"
    focusRing.color = "#F80000"
    focusRing.visible = false
    focusRing.height = handlingSizeForHD(58)
    focusRing.width = handlingSizeForHD(58)

    mainLayout = m.top.createChild("layoutGroup")
    mainLayout.layoutDirection = "vert"
    mainLayout.itemSpacings = handlingSizeForHD(75)

    topSectionLayout = mainLayout.createChild("layoutGroup")
    topSectionLayout.layoutDirection = "horiz"
    topSectionLayout.itemSpacings = handlingSizeForHD(95)

    topButtonSet1 = ["abc", "#123"]
    topButton1 = topSectionLayout.createChild("layoutGroup")
    topButton1.layoutDirection = "horiz"
    topButton1.itemSpacings = handlingSizeForHD(24)
    for each key in topButtonSet1
        keyGroup1 = topButton1.createChild("Group")
        keyGroup1.id = key
        keyFocus1 = keyGroup1.createChild("Rectangle")
        keyFocus1.height = handlingSizeForHD(48)
        keyFocus1.color = "#2E303D"
        keyFocus1.visible = true
        keyText1 = keyGroup1.createChild("label")
        keyText1.font = ghGetFont(20, "regular")
        keyText1.text = key
        keyText1.height = handlingSizeForHD(48)
        if key = "abc"
            keyFocus1.width = handlingSizeForHD(132)
            keyText1.width = handlingSizeForHD(132)
        else
            keyFocus1.width = handlingSizeForHD(140)
            keyText1.width = handlingSizeForHD(140)
        end if
        keyText1.horizAlign = "Center"
        keyText1.vertAlign = "Center"
    end for

    topButtonSet2 = ["BORRAR", "VACIAR"]
    topButton2 = topSectionLayout.createChild("layoutGroup")
    topButton2.layoutDirection = "horiz"
    topButton2.itemSpacings = handlingSizeForHD(24)
    for each key in topButtonSet2
        keyGroup2 = topButton2.createChild("Group")
        keyGroup2.id = key
        keyFocus2 = keyGroup2.createChild("Rectangle")
        keyFocus2.height = handlingSizeForHD(48)
        keyFocus2.color = "#2E303D"
        keyFocus2.visible = true
        keyText2 = keyGroup2.createChild("label")
        keyText2.font = ghGetFont(20, "regular")
        keyText2.text = key
        keyText2.height = handlingSizeForHD(48)
        if key = "BORRAR"
            keyFocus2.width = handlingSizeForHD(168)
            keyText2.width = handlingSizeForHD(168)
        else
            keyFocus2.width = handlingSizeForHD(156)
            keyText2.width = handlingSizeForHD(156)
        end if
        keyText2.horizAlign = "Center"
        keyText2.vertAlign = "Center"
    end for
    m.topSectionKeyArray = ["abc", "#123", "BORRAR", "VACIAR"]

    keyGrid = mainLayout.createChild("layoutGroup")
    keyGrid.layoutDirection = "vert"
    keyGrid.itemSpacings = handlingSizeForHD(30)
    for each keyRow in keyRows
        keyRowEach1 = keyGrid.createChild("layoutGroup")
        keyRowEach1.layoutDirection = "horiz"
        keyRowEach1.itemSpacings = handlingSizeForHD(30)
        for each key in keyRow
            keyGroup3 = keyRowEach1.createChild("Group")
            keyGroup3.id = key
            keyFocus3 = keyGroup3.createChild("Rectangle")
            keyFocus3.height = handlingSizeForHD(48)
            keyFocus3.width = handlingSizeForHD(48)
            keyFocus3.visible = false
            keyText3 = keyGroup3.createChild("label")
            keyText3.font = ghGetFont(20, "regular")
            keyText3.text = key
            keyText3.height = handlingSizeForHD(48)
            keyText3.width = handlingSizeForHD(48)
            keyText3.horizAlign = "Center"
            keyText3.vertAlign = "Center"
        end for
    end for

    if m.ifCapsAlphaNumeric = true or m.ifAlphaNumeric = true
        keyRowEach3 = m.top.createChild("Group")
        keyRowEach3.translation = [0, keyGrid.boundingRect().height + 15 + handlingSizeForHD(75) + handlingSizeForHD(48)]
        keySection1 = keyRowEach3.createChild("layoutGroup")
        keySection1.layoutDirection = "horiz"
        keySection1.itemSpacings = handlingSizeForHD(30)
        for each key in m.SpChr2
            keyGroup5 = keySection1.createChild("Group")
            keyGroup5.id = key
            keyFocus5 = keyGroup5.createChild("Rectangle")
            keyFocus5.height = handlingSizeForHD(48)
            keyFocus5.width = handlingSizeForHD(48)
            keyFocus5.visible = false
            if key = "caps"
                keyText5 = keyGroup5.createChild("Poster")
                keyText5.uri = "pkg://images/caps.png"
                keyFocus5.width = keyText5.boundingRect().width
                keyText5.translation = [0, keyFocus5.height - 10]
            else
                keyText5 = keyGroup5.createChild("label")
                keyText5.font = ghGetFont(20, "regular")
                keyText5.text = key
                keyText5.height = handlingSizeForHD(48)
                keyText5.width = handlingSizeForHD(48)
                keyText5.horizAlign = "Center"
                keyText5.vertAlign = "Center"
            end if
        end for
    end if

    if m.ifCapsAlphaNumeric = true or m.ifAlphaNumeric = true
        keyRowEach4 = m.top.createChild("Group")
        keyRowEach4.translation = [0, keyGrid.boundingRect().height + 15 + keyRowEach3.boundingRect().height + handlingSizeForHD(75) + handlingSizeForHD(48)]
        keySection2 = keyRowEach4.createChild("layoutGroup")
        keySection2.layoutDirection = "horiz"
        keySection2.itemSpacings = handlingSizeForHD(30)
        for each key in m.SpChr3
            keyGroup6 = keySection2.createChild("Group")
            keyGroup6.id = key
            keyFocus6 = keyGroup6.createChild("Rectangle")
            keyFocus6.height = handlingSizeForHD(48)
            keyFocus6.width = handlingSizeForHD(48)
            keyFocus6.visible = false
            keyText6 = keyGroup6.createChild("label")
            keyText6.font = ghGetFont(20, "regular")
            keyText6.text = key
            keyText6.height = handlingSizeForHD(48)
            keyText6.width = handlingSizeForHD(48)
            keyText6.horizAlign = "Center"
            keyText6.vertAlign = "Center"
        end for
    end if

    keyRowEach2 = m.top.createChild("Group")
    if m.ifCapsAlphaNumeric = true or m.ifAlphaNumeric = true
        keyRowEach2.translation = [handlingSizeForHD(170), keyGrid.boundingRect().height + 15 + keyRowEach3.boundingRect().height + keyRowEach4.boundingRect().height + handlingSizeForHD(75) + handlingSizeForHD(48)]
    else
        keyRowEach2.translation = [handlingSizeForHD(170), keyGrid.boundingRect().height + 15 + handlingSizeForHD(75) + handlingSizeForHD(48)]
    end if
    keySection = keyRowEach2.createChild("layoutGroup")
    keySection.layoutDirection = "horiz"
    keySection.itemSpacings = handlingSizeForHD(30)
    for each key in m.SpChr4
        keyGroup4 = keySection.createChild("Group")
        keyGroup4.id = key
        keyFocus4 = keyGroup4.createChild("Rectangle")
        keyFocus4.height = handlingSizeForHD(48)
        keyFocus4.width = handlingSizeForHD(48)
        keyFocus4.visible = false
        if key = "spacebar"
            keyText4 = keyGroup4.createChild("Poster")
            keyText4.uri = "pkg://images/spacebar.png"
            keyFocus4.width = keyText4.boundingRect().width
            keyText4.translation = [0, keyFocus4.height - 10]
        else
            keyText4 = keyGroup4.createChild("label")
            keyText4.font = ghGetFont(20, "bold")
            keyText4.text = key
            keyText4.height = handlingSizeForHD(48)
            keyText4.width = handlingSizeForHD(48)
            keyText4.horizAlign = "Center"
            keyText4.vertAlign = "Center"
        end if
    end for

end sub

function updateFieldFocus()
    row = m.keyActivityRows[m.rowIndex]
    if row <> invalid
        key = row[m.keyIndex]
        if key <> invalid
            m.focusedKey = m.top.findNode(key)
        else
            return false
        end if
        if m.focusedKey <> invalid
            ' Get the bounding rectangle of the focused key
            keyRect = m.focusedKey.boundingRect()
            ' Position the focus ring around the focused key with a small padding
            if m.rowIndex = m.keyActivityRows.count() - 1
                m.top.findNode("focusRing").translation = [handlingSizeForHD(170) + (keyRect.x - 5), (keyRect.y - 5) + (m.rowindex * (handlingSizeForHD(30) + 31) + handlingSizeForHD(75) + handlingSizeForHD(48))]
            else
                m.top.findNode("focusRing").translation = [(keyRect.x - 5), (keyRect.y - 5) + (m.rowindex * (handlingSizeForHD(30) + 35) + handlingSizeForHD(75) + handlingSizeForHD(48))]
            end if
            m.top.findNode("focusRing").width = keyRect.width + 10
            m.top.findNode("focusRing").height = keyRect.height + 10
            m.top.findNode("focusRing").visible = true
            return true
        else
            m.top.findNode("focusRing").visible = false
            return false
        end if
    else
        return false
    end if
end function

function updateTopFocus()
    key = m.topSectionKeyArray[m.lastTopIndex]
    if key <> invalid
        lastfocusedKey = m.top.findNode(key)
        lastfocusedKey.getChild(0).color = "#2E303D"
    end if
    key = m.topSectionKeyArray[m.keyIndex]
    m.top.findNode("focusRing").visible = false
    m.lastTopIndex = m.keyIndex
    if key <> invalid
        m.focusedKey = m.top.findNode(key)
        m.focusedKey.getChild(0).color = "#F80000"
        return true
    else
        return false
    end if
end function

function onKeyEvent(key, press) as boolean
    handled = false
    if press then
        if key = "left"
            m.keyIndex -= 1
            if m.topSectionFocus = false
                if updateFieldFocus()
                else
                    m.keyIndex = 0
                end if
            else
                if updateTopFocus()
                else
                    m.keyIndex = 0
                end if
            end if
            handled = true
        else if key = "options"
            if m.focusedKey <> invalid and m.focusedKey.id <> ""
                if m.focusedKey.id = "caps"
                    m.ifNumericSpecialChar = false
                    if m.ifCapsAlphaNumeric = false
                        m.ifAlphaNumeric = false
                        m.ifCapsAlphaNumeric = true
                    else
                        m.ifAlphaNumeric = true
                        m.ifCapsAlphaNumeric = false
                    end if
                    handledKeys()
                    updateFieldFocus()
                    handled = true
                end if
            end if
        else if key = "back"
            m.top.findNode("focusRing").visible = false
            m.top.setFocus(false)
            m.top.getParent().callFunc("initialFocus", "")
            m.keyIndex = 0
            m.rowIndex = 0
            handled = true
        else if key = "right"
            m.keyIndex += 1
            if m.topSectionFocus = false
                if updateFieldFocus()
                else
                    m.top.findNode("focusRing").visible = false
                    m.top.setFocus(false)
                    m.top.getParent().callFunc("initialFocus", "")
                    m.keyIndex = 0
                    m.rowIndex = 0
                end if
            else
                if updateTopFocus()
                else
                    m.top.findNode("focusRing").visible = false
                    m.top.setFocus(false)
                    m.top.getParent().callFunc("initialFocus", "")
                    m.keyIndex = 0
                    m.rowIndex = 0
                end if
            end if
            handled = true
        else if key = "up"
            m.rowIndex -= 1
            if updateFieldFocus()
                handled = true
            else
                m.keyIndex = 0
                m.rowIndex = 0
                m.topSectionFocus = true
                updateTopFocus()
                handled = true
            end if
        else if key = "down"
            m.rowIndex += 1
            if m.topSectionFocus = false
                if m.rowIndex = m.keyActivityRows.count() - 1
                    m.keyIndex = 0
                end if
            end if
            if m.topSectionFocus = false
                updateFieldFocus()
            else if m.topSectionFocus = true
                key = m.topSectionKeyArray[m.lastTopIndex]
                if key <> invalid
                    lastfocusedKey = m.top.findNode(key)
                    lastfocusedKey.getChild(0).color = "#2E303D"
                end if
                m.topSectionFocus = false
                m.keyIndex = 0
                m.rowIndex = 0
                updateFieldFocus()
            else
                m.rowIndex = m.keyActivityRows.count() - 1
            end if
            handled = true
        else if key = "OK"
            if m.focusedKey <> invalid and m.focusedKey.id <> ""
                if m.focusedKey.id = "abc"
                    m.ifNumericSpecialChar = false
                    m.ifCapsAlphaNumeric = false
                    m.ifAlphaNumeric = true
                    handledKeys()
                    updateFieldFocus()
                else if m.focusedKey.id = "#123"
                    m.ifNumericSpecialChar = true
                    m.ifCapsAlphaNumeric = false
                    m.ifAlphaNumeric = false
                    handledKeys()
                    updateFieldFocus()
                else if m.focusedKey.id = "caps"
                    m.ifNumericSpecialChar = false
                    if m.ifCapsAlphaNumeric = false
                        m.ifAlphaNumeric = false
                        m.ifCapsAlphaNumeric = true
                    else
                        m.ifAlphaNumeric = true
                        m.ifCapsAlphaNumeric = false
                    end if
                    handledKeys()
                    updateFieldFocus()
                else
                    m.top.keyPress = m.focusedKey.id
                end if
                handled = true
            end if
        end if
    end if

    return handled
end function
