' AMCOGATE

sub PurchasePromogate()
  m.logger.debug("Purchase PromoGate Init")

  PromoGateRun(invalid)
end sub

sub PromoGateRun(newState = invalid, info = {})
  m.logger.debug("PromoGateRun", { state: newState, info: info })

  setLoading(false)

  if newState = invalid then
    ScrCodigo = CreateObject("roSGNode", "CheckoutFieldsEntry")
    ScrCodigo.id = "CodigoPromo"
    ScrCodigo.ObserveField("wasClosed", "CodigoPromo_Return")
    ' Get buyData from the current buy flow
    buyData = {
      buyType: ghGetChild(m.buy, "data.button.oneofferdesc", "compra o renta")
      buyPrice: ghGetChild(m.buy, "data.button.price", "00.00")
      buyCurrency: ghGetChild(m.buy, "data.button.currency", "$")
      buyProductType: ghGetChild(m.buy, "data.button.producttype")
      buyPeriodo: ghReplaceStr(ghReplaceStr(ghGetChild(m.buy, "data.button.periodicity", ""), "month", "mes"), "year", "año")
      buyIsAddon: ghGetChild(m.buy, "data.button.oneoffertype", invalid)
      buyFamily: ghGetChild(m.buy, "data.button.family", invalid)
      buyBanner: ghGetChild(m.buy, "data.button.banner", "")
      oneoffertype: ghGetChild(m.buy, "data.button.oneoffertype", "")

      contentImagen: ghGetChild(m.buy, "data.group.content.imagenCheckout", "")
      contentTitle: ghGetChild(m.buy, "data.group.content.TITLE", "titulo")
      contentTitleSeason: ghGetChild(m.buy, "data.group.content.titleSeason", "")
      contentId: ghGetChild(m.buy, "data.contentId", "")
      content_name: ghGetChild(m.buy, "data.content_name", "")
      content_type: ghGetChild(m.buy, "data.content_type", "")
      content_category: ghGetChild(m.buy, "data.content_category", "")

      paymentMethod: ghGetChild(m.buy, "states.checkout.paymentMethod.selected.gateway"),
      screen_name: "promogate",
      screen_class: "/promogate",

    }
    GA4Event("purchase_promogate", buyData)
    m.top.routerChild = { page: ScrCodigo,
      fields: {
        checkoutFieldType: "promogate"
        buyData: buyData
      }
    }

  else if newState = "back" then
    JumpTo("checkout")

  else if newState = "go" then
    setLoading(true)

    apiConfirm = ghCallApi("PaywayConfirmLite", "CodigoPromo_Payway_Return", "CodigoPromo_Payway_ReturnError", false)
    apiConfirm.setFields({
      buylink: ghGetChild(m.buy, "states.purchase.paymentMethod.data.buylink")
      pincode: ghGetChild(info, "pin", "")
    })
    apiConfirm.control = "run"

  else if newState = "ok" then
    JumpTo("purchase", "ok")

  else if newState = "error" then
    showMessageError(ghGetChild(info, "data", {}))

  else
    JumpTo("purchase", "fail")

  end if
end sub

sub CodigoPromo_Return(event)
  scr = event.getRoSGNode()

  m.logger.debug("CodigoPromo_return", { opcion: scr.value.opcion, data: scr.value.data })

  if scr.value.opcion = "BACK" or scr.value.opcion = "CANCEL" then
    PromoGateRun("back")
  else if scr.value.opcion = "CERRAR" then
    ' Handle cancel button from TicketScreen - exit buy flow and return to Vcard
    print "TicketScreen cancel button clicked, exiting buy flow"
    JumpTo("out", "ok")
  else if scr.value.opcion = "SELECT" then
    ' HARDCODED TEST: Use "2024" to test ticket screen flow directly (bypass API)
    promoCode = scr.value.data
    if promoCode = "2024" then
      ' Test code - go directly to ticket screen without API call
      print "PromoGate: Test code 2024 detected, going directly to ticket screen"
      PromoGate_TicketScreenLanding({ test: true, promoCode: "2024" })
    else
      ' Invalid promo code - show error message
      PromoGateRun("error", {
        data: {
          message: "Código promocional inválido. Por favor, ingrese un código válido."
          code: "INVALID_PROMO_CODE"
        }
      })
    end if

  end if
end sub

sub CodigoPromo_Payway_Return(event)
  data = event.getData()

  m.logger.debug("CodigoPromo_Payway_return", { data: data })

  ' Show ticket screen instead of going directly to success
  PromoGate_TicketScreenLanding(data)
end sub

sub CodigoPromo_Payway_ReturnError(event)
  data = event.getData()

  m.logger.error("CodigoPromo_Payway_returnError", { data: data })

  PromoGateRun("error", { data: data })
end sub

sub PromoGate_TicketScreenLanding(data)
  print "=== PromoGate TicketScreenLanding START ==="
  print "PromoGate TicketScreenLanding data:" data
  print "m.buy.data:" m.buy.data

  ' Check if hidden_confirm_trans_config is set, similar to stateTicket.brs logic
  ' DISABLED FOR TESTING - Comment out to always show ticket screen
  ' hiddenConfirmTransConfig = ghGetChild(m.buy.data, "hidden_confirm_trans_config", false)
  ' print "hiddenConfirmTransConfig:" hiddenConfirmTransConfig

  ' if hiddenConfirmTransConfig then
  '   ' Don't show ticket screen, proceed to success flow instead
  '   print "PromoGate: Hidden config is true, skipping ticket screen"
  '   PromoGateRun("ok")
  '   return
  ' end if

  print "PromoGate: Forcing ticket screen display for testing"

  print "PromoGate: Creating TicketScreen..."
  ticketInfo = data
  if ticketInfo = invalid then ticketInfo = invalid
  TicketScreenFinal = CreateObject("roSGNode", "TicketScreen")
  print "PromoGate: TicketScreen created:" TicketScreenFinal

  TicketScreenFinal.id = "PromoGateTicketScreenFinal"
  TicketScreenFinal.ObserveField("wasClosed", "PromoGate_TicketScreen_Return")

  m.logger.debug("PromoGate TicketScreenFinal", { checkout: m.buy.states["checkout"] })
  print "PromoGate TicketScreenFinal checkout data:" m.buy.states["checkout"]

  ' Pass buy data to ticket screen - use mock data for testing if checkout data is invalid
  checkoutData = m.buy.states["checkout"]
  if checkoutData = invalid then
    print "PromoGate: checkout data is invalid, using mock data for testing"
    checkoutData = {
      buyPrice: "9.99"
      buyCurrency: "$"
      buyProductType: "rent"
      oneoffertype: "download_rent"
      contentTitle: "Test Content"
      contentImagen: ""
      banner: ""
      buyPeriodo: "24 horas"
      ivaText: "IVA incluido"
    }
  end if

  TicketScreenFinal.buyData = checkoutData
  print "PromoGate: buyData set on TicketScreen:" checkoutData

  print "PromoGate: Setting routerChild to show TicketScreen..."
  m.top.routerChild = {
    page: TicketScreenFinal
  }
  print "=== PromoGate TicketScreenLanding END ==="
end sub

sub PromoGate_TicketScreen_Return(event)
  scr = event.getRoSGNode()
  print "PromoGate_TicketScreen_Return :" scr.value

  m.logger.debug("PromoGate_TicketScreen_Return", { option: scr.value.option })

  if scr.value.option = "BACK" or scr.value.option = "CANCEL" then
    ' Go back to checkout
    JumpTo("checkout")
  else if scr.value.option = "CERRAR" then
    ' Handle cancel button from TicketScreen - exit buy flow and return to Vcard
    m.logger.debug("PromoGate TicketScreen cancel button clicked, exiting buy flow")
    JumpTo("out", "ok")
  else if scr.value.option = "ACCEPT" or scr.value.option = "SELECT" then
    ' Proceed to success
    PromoGateRun("ok")
  else
    m.logger.error("PromoGate TicketScreen: no se reconoce la seleccion", { option: scr.value.option })
    PromoGateRun("ok")
  end if
end sub