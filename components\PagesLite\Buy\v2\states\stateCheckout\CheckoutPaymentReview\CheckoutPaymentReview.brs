' CheckoutPaymentReview

sub Init()
  m.top.debug = true

  m.botonera = m.top.findNode("botonera")
  m.viewVOD = m.top.findNode("viewVOD")
  m.accept = m.top.findNode("accept")
  m.select = m.top.findNode("select")
  m.cancel = m.top.findNode("cancel")
  m.payMethodLabel = m.top.findNode("payMethod")

  componentsInit()
end sub

sub componentsInit()
  ' m.accept.setFields({ text: ghTranslate("checkout_access_option_button_subscription", "") })

  m.accept.setFields({ text: ghTranslate("Transaccionales_Checkout_TextoBotonPrimarioAgregarmdp", "") })
  m.payMethodLabel.setFields({
    font: ghGetFont(18, "bold")
    horizAlign: "center"
    visible: false
  })

  cancelText = ghTranslate("Transaccionales_Checkout_TextoBotonSecundarioCancelar", "")
  if cancelText = "" or cancelText = invalid then
    cancelText = "CANCELAR"
  end if

  m.cancel.setFields({ text: cancelText })

  m.accept.setFields({
    text: ghTranslate("Transaccionales_Renta_TextoBotonPrimario", "INGRESA PARA CONTINUAR")
  })
  m.cancel.visible = false

  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "closeScreen")
end sub

sub updateFieldFocus()
  turnFocusTo("botonera")
end sub

function onKeyEvent(key, press)
  handled = false

  if press then
    if key <> "back" then
      changeFocusBasedOnKey(key)
      handled = true
    end if
  end if

  return handled
end function

sub OnButtonSelected(event)
  data = event.getData()
  child = event.getRoSGNode()

  m.logger.debug("OnButtonSelected", { option: child.value, data: data })

  if data then
    if child.value <> invalid then
      closeScreen(child.value)
    end if
  end if
end sub

sub closeScreen(value = "BACK", data = invalid)
  if value <> invalid then
    m.top.value = {
      option: value
      data: data
    }
  end if

  m.top.close = true
end sub

sub updateBuyData(event)
  data = event.getData()

  if ghGetChild(data, "paymentMethod", "") <> "" then
    m.payMethodLabel.setFields({
      text: ghTranslate("Transaccionales_Checkout_TextoPagaCon", "Paga con") + " " + ghGetChild(data, "paymentMethod", "")
      visible: true
    })
    'TEMP LOGIC NEED TO REMOVE
    if ghGetChild(data, "account", "") <> "" then
      m.payMethodLabel.setFields({
        text: ghTranslate("doconfirm_paywith_text", "Paga con ") + " " + ghGetChild(data, "paymentMethod", "") + " ****" + Right(ghGetChild(data, "account", ""), 4)
        visible: true
      })
    end if

    m.select.setFields({ text: ghTranslate("Transaccionales_Checkout_TextoBotonSecundarioCambiarmdp", "") ,
    backcolor: "#2E303D",
    selBackColor:"#2E303D"
  })

    if ghGetChild(data, "buyProductType") = "CV_PREBUY" then
      print "purchase a movie"
      m.accept.setFields({ text: ghTranslate("Transaccionales_Checkout_TextoBotonPrimarioComprar", "") })
    end if

    if ghGetChild(data, "buyProductType") = "CV_STDRENT" then
      print "renting a movie"
      m.accept.setFields({ text: ghTranslate("Transaccionales_Checkout_TextoBotonPrimarioRentar", "") })
    end if

    if ghGetChild(data, "buyProductType") = "CV_MENSUAL" then
      print "subscription of addons"
      m.accept.setFields({ text: ghTranslate("Transaccionales_Checkout_TextoBotonPrimarioContratar", "") })
    end if
    ' m.accept.setFields({ text: ghTranslate("Transaccionales_Checkout_TextoBotonPrimarioContratar", "") })

    m.accept.visible = true
    m.select.translation = [460, 600]
    m.cancel.visible = false
  else
    m.accept.visible = false
    m.select.setFields({
      text: ghTranslate("Transaccionales_Checkout_TextoBotonPrimarioAgregarmdp", "ADD PAYMENT METHOD"),
      translation: [460, 500]
    })
    m.cancel.setFields({
      text: ghTranslate("Transaccionales_Checkout_TextoBotonSecundarioCancelar", "CANCEL"),
      visible: true
    })
  end if
  contentType = ghGetChild(data, "content_type", "movie")
  productData1 = {
    content_availability: ghGetChild(data, "buyType", "compra o renta"),
    content_price: ghGetChild(data, "buyCurrency", "$") + ghGetChild(data, "buyPrice", "00.00"),
    ' isAddon: ghGetChild(data, "buyIsAddon", invalid),
    subscriptions: ghGetChild(data, "buyFamily", invalid),
    content_id: ghGetChild(data, "contentId", ""),
    content_name: ghGetChild(data, "content_name", ""),
    content_type: contentType,
    content_category: ghGetChild(data, "content_category", ""),
    payment_type: ghGetChild(data, "paymentMethod", "")
    user_type: getUserTypeGA4(true),
    country: getCountryCode(ghGetRegistry("country_code", "user")),
    user_id: ghGetRegistry("user_id", "user"),
    screen_name: "checkoutvod",
    screen_class: "/checkoutvod",
  }
  GA4Event("CheckOut_review", productData1)

  m.viewVOD.data = data
  m.viewVOD.visible = true
end sub