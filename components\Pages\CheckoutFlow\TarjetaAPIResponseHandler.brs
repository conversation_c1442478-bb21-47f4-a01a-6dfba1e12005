sub TarjetaAPI_ReturnOk(event)
    data = event.getData()
    ?"data===>"data
    if data.cliente <> invalid
        if data.cliente.id <> invalid
            m.client_id = data.cliente.id
            'apiConfirm = ghCallApi("TarjetaAPI", "TarjetaAPI_ReturnOk", "TarjetaAPI_ReturnFails", false)
            'apiConfirm.apiCallType = "addUserForTarjeta"
            'apiConfirm.postBody = m.userCCBody
            'apiConfirm.control = "RUN"
            cardDetails = m.top.cardDetails
            cardDetails.default = true
            cardDetails.client_id = m.client_id
            cardDetails.cargo_unico = true
            cardDetails.direccion = m.userCCBody.direccion
            ?"cardDetails==>"formatJSON(cardDetails)
            apiConfirm = ghCallApi("TarjetaAPI", "TarjetaAPI_ReturnOk", "TarjetaAPI_ReturnFails", false)
            apiConfirm.apiCallType = "addNewTarjeta"
            apiConfirm.postBody = cardDetails
            apiConfirm.control = "RUN"
        end if
    else if data.tarjeta <> invalid
        m.selectedCard = data.tarjeta
        ?"m.selectedCard==>"formatJSON(m.selectedCard)
        apiConfirm = ghCallApi("BuyConfirm_Tarjeta", "BuyConfirm_Tarjeta_ReturnOk", "Tarjeta_ReturnFails", false)
        apiConfirm.buylink = m.top.buyData.buylink
        apiConfirm.buyToken = m.top.buyData.buyToken
        extraParams = {}
        extraParams.client_id = m.client_id
        extraParams.card_type = "1"
        extraParams.card_token = m.selectedCard.token
        extraParams.device_fingerprint = ghGetRegistry("HKS") + "-" + getCurrentTimeHHMMSS()
        apiConfirm.extraParams = extraParams
        apiConfirm.control = "run"
    else if data.token<>invalid
      
     '   ?"data==>"formatJSON(data)
    '    apiConfirm = ghCallApi("TarjetaAPI", "TarjetaAPI_ReturnOk", "TarjetaAPI_ReturnFails", false)
    '    apiConfirm.apiCallType = "checkAvailableTarjeta"
    '    apiConfirm.client_id = m.client_id
    '    apiConfirm.control = "RUN"
   ' else if data.tarjetas <> invalid and data.tarjetas.data <> invalid and data.tarjetas.data.Count() > 0
        ?"data==>"formatJSON(data)
        ?"buyData api==>"formatJSON(m.top.buyData)
        'm.selectedCard = data.tarjetas.data[0]
        cardDetails = m.top.cardDetails
        cardDetails.default = true
        cardDetails.client_id = m.client_id
        cardDetails.cargo_unico = true
        cardDetails.direccion = m.userCCBody.direccion
        ?"cardDetails==>"formatJSON(cardDetails)
        apiConfirm = ghCallApi("TarjetaAPI", "TarjetaAPI_ReturnOk", "TarjetaAPI_ReturnFails", false)
        apiConfirm.apiCallType = "addNewTarjeta"
        apiConfirm.postBody = cardDetails
        apiConfirm.control = "RUN"


       ' apiConfirm = ghCallApi("BuyConfirm_Tarjeta", "BuyConfirm_Tarjeta_ReturnOk", "Tarjeta_ReturnFails", false)
       ' apiConfirm.buylink = m.top.buyData.buylink
       ' apiConfirm.buyToken = m.top.buyData.buyToken
       ' extraParams = {}
       ' extraParams.client_id = m.client_id
       ' extraParams.card_type = "2"
       ' extraParams.card_token = m.selectedCard.token
       ' extraParams.device_fingerprint = ghGetRegistry("HKS") + "-" + getCurrentTimeHHMMSS()
       ' apiConfirm.extraParams = extraParams
       ' apiConfirm.control = "run"
        
    end if
end sub

sub TarjetaAPI_ReturnFails(event)
    data = event.getData()
    ?"response here is ==>"formatJSON(data)

    
    apiConfirm = ghCallApi("BuyConfirm_Tarjeta", "BuyConfirm_Tarjeta_ReturnOk", "Tarjeta_ReturnFails", false)
    apiConfirm.buylink = m.top.buyData.buylink
    apiConfirm.buyToken = m.top.buyData.buyToken
    extraParams = {}
    extraParams.client_id = m.client_id
    extraParams.card_type = "2"
    extraParams.card_token = m.selectedCard.token
    extraParams.device_fingerprint = ghGetRegistry("HKS") + "-" + getCurrentTimeHHMMSS()
    apiConfirm.extraParams = extraParams
    apiConfirm.control = "run"
end sub