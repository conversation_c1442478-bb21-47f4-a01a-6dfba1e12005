' AMCOGATE

sub PurchaseAmcoGate()
  m.logger.debug("Purchase AmcoGate Init")

  AmcoGateRun()
end sub

sub AmcoGateRun(newState = invalid, info = {})
  m.logger.debug("AmcoGateRun", { state: newState, info: info })

  setLoading(false)

  if newState = invalid then
    AmcoGateCheck()

  else if newState = "go" then
    setLoading(true)
    AmcoGateGo()

  else if newState = "ok" then
    JumpTo("purchase", "ok")

  else if newState = "error" then
    showMessageError(ghGetChild(info, "data", {}))

  else if newState = "missingparameters" then
    JumpTo("purchase", "missingparameters")

  else
    JumpTo("purchase", "fail")

  end if
end sub

sub AmcoGateCheck()
  data = ghGetChild(m.buy, "states.purchase.paymentMethod")

  m.logger.debug("Purchase AmcoGate Check", { data: data })

  if data <> invalid then
    m.buy.states["purchase"].method = {
      parameters: {
        link: ghGetChild(data, "data.buyLink", "")
        buyToken: ghGetChild(data, "data.buyToken", "")
        object_type: ghGetChild(data, "data.object_type", "")
        access_code: ghGetChild(data, "data.access_code", "")
      }
    }
    AmcoGateRun("go")
  else
    AmcoGateRun("missingparameters")
  end if
end sub

sub AmcoGateGo()
  data = ghGetChild(m.buy.states, "purchase.method.parameters")

  if data <> invalid then
    apiConfirm = ghCallApi("BuyConfirmLite", "AmcoGateGo_ReturnOk", "AmcoGateGo_ReturnFails", false)
    apiConfirm.setFields(data)
    apiConfirm.control = "run"
  end if
end sub

sub AmcoGateGo_ReturnOk(event)
  data = event.getData()

  m.logger.debug("Purchase AmcoGate Go Ok", { data: data })

  ' Show ticket screen instead of going directly to success
  AmcoGate_TicketScreenLanding(data)
end sub

sub AmcoGateGo_ReturnFails(event)
  data = event.getData()

  m.logger.error("Purchase AmcoGate Go Fails", { data: data })

  AmcoGateRun("error", { data: data })
end sub

sub AmcoGate_TicketScreenLanding(data)
  print "AmcoGate TicketScreenLanding :" data

  ' Check if hidden_confirm_trans_config is set, similar to stateTicket.brs logic
  hiddenConfirmTransConfig = ghGetChild(m.buy.data, "hidden_confirm_trans_config", false)

  if hiddenConfirmTransConfig then
    ' Don't show ticket screen, proceed to success flow instead
    AmcoGateRun("ok")
    return
  end if

  ticketInfo = data
  if ticketInfo = invalid then ticketInfo = invalid
  TicketScreenFinal = CreateObject("roSGNode", "TicketScreen")
  TicketScreenFinal.id = "AmcoGateTicketScreenFinal"
  TicketScreenFinal.ObserveField("wasClosed", "AmcoGate_TicketScreen_Return")

  m.logger.debug("AmcoGate TicketScreenFinal", { checkout: m.buy.states["checkout"] })
  print "AmcoGate TicketScreenFinal :" m.buy.states["checkout"]

  ' Pass buy data to ticket screen
  TicketScreenFinal.buyData = m.buy.states["checkout"]

  m.top.routerChild = {
    page: TicketScreenFinal
  }
end sub

sub AmcoGate_TicketScreen_Return(event)
  scr = event.getRoSGNode()
  print "AmcoGate_TicketScreen_Return :" scr.value

  m.logger.debug("AmcoGate_TicketScreen_Return", { option: scr.value.option })

  if scr.value.option = "BACK" or scr.value.option = "CANCEL" then
    ' Go back to checkout
    JumpTo("checkout")
  else if scr.value.option = "CERRAR" then
    ' Handle cancel button from TicketScreen - exit buy flow and return to Vcard
    m.logger.debug("AmcoGate TicketScreen cancel button clicked, exiting buy flow")
    JumpTo("out", "ok")
  else if scr.value.option = "ACCEPT" or scr.value.option = "SELECT" then
    ' Proceed to success
    AmcoGateRun("ok")
  else
    m.logger.error("AmcoGate TicketScreen: no se reconoce la seleccion", { option: scr.value.option })
    AmcoGateRun("ok")
  end if
end sub