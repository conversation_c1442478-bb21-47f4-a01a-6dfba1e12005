<?xml version="1.0" encoding="UTF-8"?>

<component name="TarjetaAPI" extends="GHApiTask">
  <script type="text/brightscript" uri="TarjetaAPI.brs" />

  <interface>
    <!-- interfaz de entrada -->
    <field id="postBody" type="assocarray" alwaysNotify="true"/>
    <field id="queryParam" type="string" alwaysNotify="true"/>
    <field id="apiCallType" type="string" alwaysNotify="true"/>
    <field id="client_id" type="string" alwaysNotify="true"/>

  </interface>
</component>
