sub PurchaseClaroPagosGate()
    m.logger.debug("Purchase PromoGate Init")
    ClaroPagosGateRun(invalid)
end sub

sub ClaroPagosGateRun(newState = invalid, info = {})
    ' m.logger.debug("PromoGateRun", { state: newState, info: info })
    print "ClaroPagosGateRunlogs" ; newState
    setLoading(false)
    if newState = invalid then
        ScrCodigo = CreateObject("roSGNode", "CheckoutFieldsEntry")
        ' ScrCodigo.checkoutFieldType = "hubfacturafijagate"
        ScrCodigo.id = "claropagosgate"
        ScrCodigo.ObserveField("wasClosed", "ClaroPagosGate_Return")
        ' Get buyData from the current buy flow
        buyData = {
            buyType: ghGetChild(m.buy, "data.button.oneofferdesc", "compra o renta")
            buyPrice: ghGetChild(m.buy, "data.button.price", "00.00")
            buyCurrency: ghGetChild(m.buy, "data.button.currency", "$")
            buyProductType: ghGetChild(m.buy, "data.button.producttype")
            buyPeriodo: ghReplaceStr(ghReplaceStr(ghGetChild(m.buy, "data.button.periodicity", ""), "month", "mes"), "year", "año")
            buyIsAddon: ghGetChild(m.buy, "data.button.oneoffertype", invalid)
            buyFamily: ghGetChild(m.buy, "data.button.family", invalid)
            buyBanner: ghGetChild(m.buy, "data.button.banner", "")
            oneoffertype: ghGetChild(m.buy, "data.button.oneoffertype", "")

            contentImagen: ghGetChild(m.buy, "data.group.content.imagenCheckout", "")
            contentTitle: ghGetChild(m.buy, "data.group.content.TITLE", "titulo")
            contentTitleSeason: ghGetChild(m.buy, "data.group.content.titleSeason", "")
            contentId: ghGetChild(m.buy, "data.contentId", "")
            content_name: ghGetChild(m.buy, "data.content_name", "")
            content_type: ghGetChild(m.buy, "data.content_type", "")
            content_category: ghGetChild(m.buy, "data.content_category", "")

            paymentMethod: ghGetChild(m.buy, "states.checkout.paymentMethod.selected.gateway"),
            screen_name: "claropagosgate",
            screen_class: "/claropagosgate",
            buylink: ghGetChild(m.buy, "states.purchase.paymentMethod.data.buylink")
            buytoken: ghGetChild(m.buy, "states.purchase.paymentMethod.data.buytoken")
        }

        GA4Event("purchase_claropagosgate", buyData)

        m.top.routerChild = { page: ScrCodigo,
            fields: {
                checkoutFieldType: "claropagosgate"
                buyData: buyData
            }
        }
    else if newState = "back" then
        JumpTo("checkout")

    else if newState = "go" then
        setLoading(true)
        apiConfirm = ghCallApi("BuyConfirmLite", "ClaroPagosGate_Payway_Return", "ClaroPagosGate_Payway_ReturnError", false)

        apiConfirm.setFields({
            link: ghGetChild(m.buy, "states.purchase.paymentMethod.data.buylink")
            buyToken: ghGetChild(m.buy, "states.purchase.paymentMethod.data.buyToken")
            extra_params: ghGetChild(info, "data", {})
        })
        apiConfirm.control = "run"

    else if newState = "ok" then
        JumpTo("purchase", "ok")

    else if newState = "error" then
        showMessageError(ghGetChild(info, "data", {}))

    else
        JumpTo("purchase", "fail")

    end if
end sub

sub ClaroPagosGate_Return(event)
    scr = event.getRoSGNode()

    ' Add null check to prevent "Dot" operator error
    if scr <> invalid and scr.value <> invalid then
        if scr.value.opcion = "BACK" or scr.value.opcion = "CANCEL" then
            ClaroPagosGateRun("back")
        else if scr.value.opcion = "CERRAR" then
            ' Handle cancel button from TicketScreen - exit buy flow and return to Vcard
            print "TicketScreen cancel button clicked, exiting buy flow"
            JumpTo("out", "ok")
        else if scr.value.opcion = "SELECT" then
            ClaroPagosGateRun("go", scr.value.data)
        end if
    else
        ' Handle invalid scr object - default to back action
        print "ClaroPagosGate_Return: Invalid scr object, defaulting to back"
        ClaroPagosGateRun("back")
    end if
end sub

sub ClaroPagosGate_Payway_Return(event)
    data = event.getData()
    print "ClaroPagosGate_Payway_Return" ; data

    m.logger.debug("ClaroPagosGate_Payway_return", { data: data })

    ' Show ticket screen instead of going directly to success
    ClaroPagosGate_TicketScreenLanding(data)
end sub

sub ClaroPagosGate_Payway_ReturnError(event)
    data = event.getData()
    print "ClaroPagosGate_Payway_ReturnError" ; data

    m.logger.error("ClaroPagosGate_Payway_returnError", { data: data })

    ClaroPagosGateRun("error", { data: data })
end sub

sub ClaroPagosGate_TicketScreenLanding(data)
    print "ClaroPagosGate TicketScreenLanding :" data

    ' Check if hidden_confirm_trans_config is set, similar to stateTicket.brs logic
    hiddenConfirmTransConfig = ghGetChild(m.buy.data, "hidden_confirm_trans_config", false)

    if hiddenConfirmTransConfig then
        ' Don't show ticket screen, proceed to success flow instead
        ClaroPagosGateRun("ok")
        return
    end if

    ticketInfo = data
    if ticketInfo = invalid then ticketInfo = invalid
    TicketScreenFinal = CreateObject("roSGNode", "TicketScreen")
    TicketScreenFinal.id = "ClaroPagosGateTicketScreenFinal"
    TicketScreenFinal.ObserveField("wasClosed", "ClaroPagosGate_TicketScreen_Return")

    m.logger.debug("ClaroPagosGate TicketScreenFinal", { checkout: m.buy.states["checkout"] })
    print "ClaroPagosGate TicketScreenFinal :" m.buy.states["checkout"]

    ' Pass buy data to ticket screen
    TicketScreenFinal.buyData = m.buy.states["checkout"]

    m.top.routerChild = {
        page: TicketScreenFinal
    }
end sub

sub ClaroPagosGate_TicketScreen_Return(event)
    scr = event.getRoSGNode()
    print "ClaroPagosGate_TicketScreen_Return :" scr.value

    m.logger.debug("ClaroPagosGate_TicketScreen_Return", { option: scr.value.option })

    if scr.value.option = "BACK" or scr.value.option = "CANCEL" then
        ' Go back to checkout
        JumpTo("checkout")
    else if scr.value.option = "CERRAR" then
        ' Handle cancel button from TicketScreen - exit buy flow and return to Vcard
        m.logger.debug("ClaroPagosGate TicketScreen cancel button clicked, exiting buy flow")
        JumpTo("out", "ok")
    else if scr.value.option = "ACCEPT" or scr.value.option = "SELECT" then
        ' Proceed to success
        ClaroPagosGateRun("ok")
    else
        m.logger.error("ClaroPagosGate TicketScreen: no se reconoce la seleccion", { option: scr.value.option })
        ClaroPagosGateRun("ok")
    end if
end sub