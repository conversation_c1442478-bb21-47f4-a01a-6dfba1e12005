sub PurchaseClaroPagosGate()
    m.logger.debug("Purchase PromoGate Init")
    ClaroPagosGateRun(invalid)
end sub

sub ClaroPagosGateRun(newState = invalid, info = {})
    ' m.logger.debug("PromoGateRun", { state: newState, info: info })
    print "ClaroPagosGateRunlogs" ; newState
    setLoading(false)
    if newState = invalid then
        ScrCodigo = CreateObject("roSGNode", "CheckoutFieldsEntry")
        ' ScrCodigo.checkoutFieldType = "hubfacturafijagate"
        ScrCodigo.id = "claropagosgate"
        ScrCodigo.ObserveField("wasClosed", "ClaroPagosGate_Return")
        ' Get buyData from the current buy flow
        buyData = {
            buyType: ghGetChild(m.buy, "data.button.oneofferdesc", "compra o renta")
            buyPrice: ghGetChild(m.buy, "data.button.price", "00.00")
            buyCurrency: ghGetChild(m.buy, "data.button.currency", "$")
            buyProductType: ghGetChild(m.buy, "data.button.producttype")
            buyPeriodo: ghReplaceStr(ghReplaceStr(ghGetChild(m.buy, "data.button.periodicity", ""), "month", "mes"), "year", "año")
            buyIsAddon: ghGetChild(m.buy, "data.button.oneoffertype", invalid)
            buyFamily: ghGetChild(m.buy, "data.button.family", invalid)
            buyBanner: ghGetChild(m.buy, "data.button.banner", "")
            oneoffertype: ghGetChild(m.buy, "data.button.oneoffertype", "")

            contentImagen: ghGetChild(m.buy, "data.group.content.imagenCheckout", "")
            contentTitle: ghGetChild(m.buy, "data.group.content.TITLE", "titulo")
            contentTitleSeason: ghGetChild(m.buy, "data.group.content.titleSeason", "")
            contentId: ghGetChild(m.buy, "data.contentId", "")
            content_name: ghGetChild(m.buy, "data.content_name", "")
            content_type: ghGetChild(m.buy, "data.content_type", "")
            content_category: ghGetChild(m.buy, "data.content_category", "")

            paymentMethod: ghGetChild(m.buy, "states.checkout.paymentMethod.selected.gateway"),
            screen_name: "claropagosgate",
            screen_class: "/claropagosgate",
            buylink: ghGetChild(m.buy, "states.purchase.paymentMethod.data.buylink")
            buytoken: ghGetChild(m.buy, "states.purchase.paymentMethod.data.buytoken")
        }

        GA4Event("purchase_claropagosgate", buyData)

        m.top.routerChild = { page: ScrCodigo,
            fields: {
                checkoutFieldType: "claropagosgate"
                buyData: buyData
            }
        }
    else if newState = "back" then
        JumpTo("checkout")

    else if newState = "go" then

    else if newState = "ok" then
        JumpTo("purchase", "ok")

    else if newState = "error" then
        showMessageError(ghGetChild(info, "data", {}))

    else
        JumpTo("purchase", "fail")

    end if
end sub

sub ClaroPagosGate_Return(event)
    scr = event.getRoSGNode()

    ' Add null check to prevent "Dot" operator error
    if scr <> invalid and scr.value <> invalid then
        if scr.value.opcion = "BACK" or scr.value.opcion = "CANCEL" then
            ClaroPagosGateRun("back")
        else if scr.value.opcion = "CERRAR" then
            ' Handle cancel button from TicketScreen - exit buy flow and return to Vcard
            print "TicketScreen cancel button clicked, exiting buy flow"
            JumpTo("out", "ok")
        else if scr.value.opcion = "SELECT" then
            ClaroPagosGateRun("go", scr.value.data)
        end if
    else
        ' Handle invalid scr object - default to back action
        print "ClaroPagosGate_Return: Invalid scr object, defaulting to back"
        ClaroPagosGateRun("back")
    end if
end sub