function HubGateFieldSetup(region, checkoutFieldType = "hubgate", screenType = "")
    print "inside your meixo set" screenType, region
    if region = "mexico"
        if screenType = 1
            m.pagemetaData.translation = [handlingSizeForHD(1027), handlingSizeForHD(251)]
            m.pagemetaData.itemSpacings = [handlingSizeForHD(30)]
            title = m.pagemetaData.createChild("Label")
            title.font = ghGetFont(handlingSizeForHD(41), "regular")
            title.text = ghTranslate("MDP_Formulario_LineaMovil_TextoTitulo_" + checkoutFieldType, "")
            title.horizAlign = "left"

            GHInput1 = m.botonera.createChild("GHInput")
            GHInput1.setFields({ "id": "fieldMobileNumberInput", "height": 72, "width": handlingSizeForHD(720), "placeholder": "Número a 10 dígitos", "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyBoardType": "custom" })
            GHInput1.translation = [handlingSizeForHD(1027), handlingSizeForHD(390)]

            submitButton = m.botonera.createChild("GHButton")
            submitButton.setFields({ id: "submitButton", value: "SELECT", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#981C15", selBackColor: "#981C15", focusColor: "0xFFFFFF" })
            submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(552)]
            submitButton.text = ghTranslate("MDP_Formulario_LineaMovil_TextoBotonPrimario", "")
            submitButton.disableButton = true
            cancelButton = m.botonera.createChild("GHButton")
            cancelButton.setFields({ id: "cancelButton", value: "BACK", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
            cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(648)]
            cancelButton.text = ghTranslate("MDP_Formulario_SingleInpuMDP_Formulario_LineaMovil_TextoBotonSecundario", "")

            focusMappingHandler(checkoutFieldType)

        else
            print "inside your meixo set" screenType
            m.pagemetaData.translation = [handlingSizeForHD(1027), handlingSizeForHD(141)]
            m.pagemetaData.itemSpacings = [handlingSizeForHD(30)]
            ' title = m.pagemetaData.createChild("Label")
            ' title.font = ghGetFont(handlingSizeForHD(60), "regular")
            ' title.text = ghTranslate("MDP_Formulario_LineaMovil_TextoTitulo_" + checkoutFieldType, "")
            ' title.horizAlign = "left"

            descrip = m.pagemetaData.createChild("Label")
            descrip.font = ghGetFont(handlingSizeForHD(41), "regular")
            descrip.text = ghTranslate("MDP_Formulario_LineaMovil_TextoSubTitulo1_" + checkoutFieldType, "")
            descrip.horizAlign = "left"
            descrip.width = handlingSizeForHD(720)
            descrip.wrap = true

            GHInput1 = m.botonera.createChild("GHInput")
            GHInput1.setFields({ "id": "fieldPinInput", "height": 72, "width": handlingSizeForHD(453), "placeholder": "", "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyBoardType": "custom" })
            GHInput1.translation = [handlingSizeForHD(1027), handlingSizeForHD(390)]

            submitPin = m.botonera.createChild("GHButton")
            submitPin.setFields({ id: "submitPin", value: "PIN", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
            submitPin.translation = [handlingSizeForHD(1495.5), handlingSizeForHD(390)]
            submitPin.width = handlingSizeForHD(252)
            submitPin.text = ghTranslate("MDP_Formulario_LineaMovil_Boton_Texto1_" + checkoutFieldType, "")

            submitButton = m.botonera.createChild("GHButton")
            submitButton.setFields({ id: "submitButton", value: "SELECT", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#981C15", selBackColor: "#981C15", focusColor: "0xFFFFFF" })
            submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(552)]
            submitButton.text = ghTranslate("MDP_Formulario_LineaMovil_TextoBotonPrimario", "")
            submitButton.disableButton = true
            cancelButton = m.botonera.createChild("GHButton")
            cancelButton.setFields({ id: "cancelButton", value: "BACK", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
            cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(648)]
            cancelButton.text = ghTranslate("MDP_Formulario_LineaMovil_TextoBotonSecundario", "")

            focusMappingHandler(checkoutFieldType)

        end if
    else if region = "colombia"
        m.pagemetaData.translation = [handlingSizeForHD(1027), handlingSizeForHD(250.5)]
        m.pagemetaData.itemSpacings = [handlingSizeForHD(30)]
        title = m.pagemetaData.createChild("Label")
        title.font = ghGetFont(handlingSizeForHD(41), "regular")
        title.text = ghTranslate("MDP_Formulario_TripleOption_TextoTitulo_" + checkoutFieldType, "")
        title.horizAlign = "left"

        GHInput1 = m.botonera.createChild("GHInput")
        GHInput1.setFields({ "id": "fieldDocumentInput", "height": 72, "width": handlingSizeForHD(720), "placeholder": ghTranslate("MDP_Formulario_TripleOption_Input_Texto1_" + checkoutFieldType, ""), "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyboardType": "grid", "keyBoardType": "grid" })
        GHInput1.translation = [handlingSizeForHD(1027), handlingSizeForHD(346.5)]
        GHInput1.dropDownIconVisibility = true


        GHInput2 = m.botonera.createChild("GHInput")
        GHInput2.setFields({ "id": "fieldDocumentNumberInput", "height": 72, "width": handlingSizeForHD(720), "placeholder": ghTranslate("MDP_Formulario_TripleOption_Input_Texto2_" + checkoutFieldType, ""), "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyBoardType": "custom" })
        GHInput2.translation = [handlingSizeForHD(1027), handlingSizeForHD(459)]

        GHInput3 = m.botonera.createChild("GHInput")
        GHInput3.setFields({ "id": "fieldFixedNumberInput", "height": 72, "width": handlingSizeForHD(720), "placeholder": ghTranslate("MDP_Formulario_TripleOption_Input_Texto3_" + checkoutFieldType, ""), "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyBoardType": "custom" })
        GHInput3.translation = [handlingSizeForHD(1027), handlingSizeForHD(571.5)]

        submitButton = m.botonera.createChild("GHButton")
        submitButton.setFields({ id: "submitButton", value: "SELECT", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#981C15", selBackColor: "#981C15", focusColor: "0xFFFFFF" })
        submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(691.5)]
        submitButton.text = ghTranslate("MDP_Formulario_TripleOption_TextoBotonPrimario_" + checkoutFieldType, "")
        submitButton.disableButton = true
        cancelButton = m.botonera.createChild("GHButton")
        cancelButton.setFields({ id: "cancelButton", value: "BACK", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
        cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(796.5)]
        cancelButton.text = ghTranslate("MDP_Formulario_TripleOption_TextoBotonSecundario_" + checkoutFieldType, "")

        focusMappingHandler(checkoutFieldType)

    else if region = "argentina" or region = "peru"
        if screenType = 1
            m.pagemetaData.translation = [handlingSizeForHD(1027), handlingSizeForHD(251)]
            m.pagemetaData.itemSpacings = [handlingSizeForHD(30)]
            title = m.pagemetaData.createChild("Label")
            title.font = ghGetFont(handlingSizeForHD(41), "regular")
            title.text = ghTranslate("MDP_Formulario_SingleInput_TextoTitulo_" + checkoutFieldType, "")
            title.horizAlign = "left"

            GHInput1 = m.botonera.createChild("GHInput")
            GHInput1.setFields({ "id": "fieldMobileNumberInput", "height": 72, "width": handlingSizeForHD(720), "placeholder": ghTranslate("MDP_Formulario_SingleInput_Input_Texto_" + checkoutFieldType, ""), "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyBoardType": "custom" })
            GHInput1.translation = [handlingSizeForHD(1027), handlingSizeForHD(388.5)]


            submitButton = m.botonera.createChild("GHButton")
            submitButton.setFields({ id: "submitButton", value: "SELECT", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#981C15", selBackColor: "#981C15", focusColor: "0xFFFFFF" })
            submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(633)]
            submitButton.text = ghTranslate("MDP_Formulario_SingleInput_TextoBotonPrimario_" + checkoutFieldType, "")
            submitButton.disableButton = true
            cancelButton = m.botonera.createChild("GHButton")
            cancelButton.setFields({ id: "cancelButton", value: "BACK", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
            cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(750)]
            cancelButton.text = ghTranslate("MDP_Formulario_SingleInput_TextoBotonSecundario_" + checkoutFieldType, "")

            focusMappingHandler(checkoutFieldType)

        else
            m.pagemetaData.translation = [handlingSizeForHD(1027), handlingSizeForHD(141)]
            m.pagemetaData.itemSpacings = [handlingSizeForHD(30)]
            title = m.pagemetaData.createChild("Label")
            title.font = ghGetFont(handlingSizeForHD(60), "regular")
            title.text = ghTranslate("MDP_Formulario_LineaMovil_TextoTitulo_" + checkoutFieldType, "")
            title.horizAlign = "left"

            m.descriptionLayout = CreateObject("roSGNode", "LayoutGroup")
            m.descriptionLayout.itemSpacings = [handlingSizeForHD(0)]
            m.descriptionLayout.layoutDirection = "vert"

            descrip = m.descriptionLayout.createChild("Label")
            descrip.font = ghGetFont(handlingSizeForHD(41), "regular")
            descrip.text = ghTranslate("MDP_Formulario_LineaMovil_TextoSubTitulo1_" + checkoutFieldType, "")
            descrip.horizAlign = "left"
            descrip.width = handlingSizeForHD(720)

            m.pagemetaData.appendChild(m.descriptionLayout)

            descrip2 = m.descriptionLayout.createChild("Label")
            descrip2.font = ghGetFont(handlingSizeForHD(41), "regular")
            descrip2.text = ghTranslate("MDP_Formulario_LineaMovil_TextoSubTitulo2_" + checkoutFieldType, "")
            descrip2.horizAlign = "left"
            descrip2.width = handlingSizeForHD(720)
            descrip2.translation = [descrip.translation[0], descrip.translation[1] + 5]


            GHInput1 = m.botonera.createChild("GHInput")
            GHInput1.setFields({ "id": "fieldPinInput", "height": 72, "width": handlingSizeForHD(453), "placeholder": "", "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyBoardType": "custom" })
            GHInput1.translation = [handlingSizeForHD(1027), handlingSizeForHD(388.5)]

            submitPin = m.botonera.createChild("GHButton")
            submitPin.setFields({ id: "submitPin", value: "PIN", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
            submitPin.translation = [handlingSizeForHD(1495.5), handlingSizeForHD(388.5)]
            submitPin.width = handlingSizeForHD(250.5)
            submitPin.text = ghTranslate("MDP_Formulario_LineaMovil_Boton_Texto1_" + checkoutFieldType, "")

            submitButton = m.botonera.createChild("GHButton")
            submitButton.setFields({ id: "submitButton", value: "SELECT", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#981C15", selBackColor: "#981C15", focusColor: "0xFFFFFF" })
            submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(633)]
            submitButton.text = ghTranslate("MDP_Formulario_LineaMovil_TextoBotonPrimario", "")
            submitButton.disableButton = true
            cancelButton = m.botonera.createChild("GHButton")
            cancelButton.setFields({ id: "cancelButton", value: "BACK", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
            cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(750)]
            cancelButton.text = ghTranslate("MDP_Formulario_LineaMovil_TextoBotonSecundario" + checkoutFieldType, "")

            focusMappingHandler(checkoutFieldType)

        end if
    else
        print "else part of region :" m.region, screenType
        if screenType = 1
            m.pagemetaData.translation = [handlingSizeForHD(1027), handlingSizeForHD(251)]
            m.pagemetaData.itemSpacings = [handlingSizeForHD(30)]
            title = m.pagemetaData.createChild("Label")
            title.font = ghGetFont(handlingSizeForHD(40.5), "regular")
            title.text = ghTranslate("MDP_Formulario_DoubleInput_TextoTitulo_" + checkoutFieldType, "")
            title.horizAlign = "left"

            GHInput1 = m.botonera.createChild("GHInput")
            GHInput1.setFields({ "id": "fieldMovilIDInput", "height": 72, "width": handlingSizeForHD(720), "placeholder": ghTranslate("MDP_Formulario_DoubleInput_Input_Texto1_" + checkoutFieldType, ""), "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyBoardType": "custom" })
            GHInput1.translation = [handlingSizeForHD(1027), handlingSizeForHD(388.5)]

            GHInput2 = m.botonera.createChild("GHInput")
            GHInput2.setFields({ "id": "fieldMobileNumberInput", "height": 72, "width": handlingSizeForHD(720), "placeholder": ghTranslate("MDP_Formulario_DoubleInput_Input_Texto2_" + checkoutFieldType, ""), "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyBoardType": "custom" })
            GHInput2.translation = [handlingSizeForHD(1027), handlingSizeForHD(501)]

            submitButton = m.botonera.createChild("GHButton")
            submitButton.setFields({ id: "submitButton", value: "SELECT", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], width: handlingSizeForHD(250.5), height: 72, color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#981C15", selBackColor: "#981C15", focusColor: "0xFFFFFF" })
            submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(642)]
            submitButton.text = ghTranslate("MDP_Formulario_DoubleInput_TextoBotonPrimario_" + checkoutFieldType, "")
            submitButton.disableButton = true
            cancelButton = m.botonera.createChild("GHButton")
            cancelButton.setFields({ id: "cancelButton", value: "BACK", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
            cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(738)]
            cancelButton.text = ghTranslate("MDP_Formulario_DoubleInput_TextoBotonSecundario_" + checkoutFieldType, "")

            focusMappingHandler(checkoutFieldType)
            print "find nod call :" m.top.findNode("fieldMovilIDInput")

        else
            ' m.pagemetaData.translation = [handlingSizeForHD(1027), handlingSizeForHD(141)]
            ' m.pagemetaData.itemSpacings = [handlingSizeForHD(30)]
            ' title = m.pagemetaData.createChild("Label")
            ' title.font = ghGetFont(handlingSizeForHD(60), "regular")
            ' title.text = ghTranslate("MDP_Formulario_LineaMovil_TextoTitulo_" + checkoutFieldType, "")
            ' title.horizAlign = "left"

            ' descrip = m.pagemetaData.createChild("Label")
            ' descrip.font = ghGetFont(handlingSizeForHD(41), "regular")
            ' descrip.text = ghTranslate("MDP_Formulario_LineaMovil_TextoSubTitulo1_" + checkoutFieldType, "")
            ' descrip.horizAlign = "left"
            ' descrip.width = handlingSizeForHD(720)
            ' descrip.wrap = true

            ' descrip2 = m.pagemetaData.createChild("Label")
            ' descrip2.font = ghGetFont(handlingSizeForHD(41), "regular")
            ' descrip2.text = ghTranslate("MDP_Formulario_LineaMovil_TextoSubTitulo2_" + checkoutFieldType, "")
            ' descrip2.horizAlign = "left"
            ' descrip2.width = handlingSizeForHD(720)
            ' descrip2.translation = [descrip.translation[0], descrip.translation[1] + 5]
            ' descrip2.wrap = true

            ' GHInput1 = m.botonera.createChild("GHInput")
            ' GHInput1.setFields({ "id": "fieldPinInput", "height": 72, "width": handlingSizeForHD(453), "placeholder": "", "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyBoardType": "custom" })
            ' GHInput1.translation = [handlingSizeForHD(1027), handlingSizeForHD(388.5)]

            ' submitPin = m.botonera.createChild("GHButton")
            ' submitPin.setFields({ id: "submitPin", value: "PIN", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
            ' submitPin.translation = [handlingSizeForHD(1495.5), handlingSizeForHD(388.5)]
            ' submitPin.width = handlingSizeForHD(250.5)
            ' submitPin.text = ghTranslate("MDP_Formulario_LineaMovil_Boton_Texto1_" + checkoutFieldType, "")

            ' submitButton = m.botonera.createChild("GHButton")
            ' submitButton.setFields({ id: "submitButton", value: "SELECT", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#981C15", selBackColor: "#981C15", focusColor: "0xFFFFFF" })
            ' submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(633)]
            ' submitButton.text = ghTranslate("MDP_Formulario_LineaMovil_TextoBotonPrimario", "")
            ' submitButton.disableButton = true

            ' cancelButton = m.botonera.createChild("GHButton")
            ' cancelButton.setFields({ id: "cancelButton", value: "BACK", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
            ' cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(750)]
            ' cancelButton.text = ghTranslate("MDP_Formulario_LineaMovil_TextoBotonSecundario" + checkoutFieldType, "")

            ' focusMappingHandler(checkoutFieldType)


            m.pagemetaData.translation = [handlingSizeForHD(1027), handlingSizeForHD(141)]
            m.pagemetaData.itemSpacings = [handlingSizeForHD(30)]
            title = m.pagemetaData.createChild("Label")
            title.font = ghGetFont(handlingSizeForHD(60), "regular")
            title.text = ghTranslate("MDP_Formulario_LineaMovil_TextoTitulo_" + checkoutFieldType, "")
            title.horizAlign = "left"

            descrip = m.pagemetaData.createChild("Label")
            descrip.font = ghGetFont(handlingSizeForHD(41), "regular")
            descrip.text = ghTranslate("MDP_Formulario_LineaMovil_TextoSubTitulo1_" + checkoutFieldType, "")
            descrip.horizAlign = "left"
            descrip.width = handlingSizeForHD(720)
            descrip.wrap = true

            GHInput1 = m.botonera.createChild("GHInput")
            GHInput1.setFields({ "id": "fieldPinInput", "height": 72, "width": handlingSizeForHD(453), "placeholder": "", "color": "0xFFFFFF", "selColor": "#7F8086", "focusColor": "0xFFFFFF", "placeholdercolor": "#7F8086", "password": "false", "removeDefaultFocus": true, "keyBoardType": "custom" })
            GHInput1.translation = [handlingSizeForHD(1027), handlingSizeForHD(390)]

            submitPin = m.botonera.createChild("GHButton")
            submitPin.setFields({ id: "submitPin", value: "PIN", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
            submitPin.translation = [handlingSizeForHD(1495.5), handlingSizeForHD(390)]
            submitPin.width = handlingSizeForHD(252)
            submitPin.text = ghTranslate("MDP_Formulario_LineaMovil_Boton_Texto1_" + checkoutFieldType, "")

            submitButton = m.botonera.createChild("GHButton")
            submitButton.setFields({ id: "submitButton", value: "SELECT", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#981C15", selBackColor: "#981C15", focusColor: "0xFFFFFF" })
            submitButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(552)]
            submitButton.text = ghTranslate("MDP_Formulario_LineaMovil_TextoBotonPrimario", "")
            submitButton.disableButton = true

            cancelButton = m.botonera.createChild("GHButton")
            cancelButton.setFields({ id: "cancelButton", value: "BACK", text: "", translation: [handlingSizeForHD(1027), handlingSizeForHD(500)], color: "0xFFFFFF", selColor: "#FFFFFF", width: handlingSizeForHD(720), height: 72, backcolor: "#212224", selBackColor: "#212224", focusColor: "0xFFFFFF" })
            cancelButton.translation = [handlingSizeForHD(1027), handlingSizeForHD(648)]
            cancelButton.text = ghTranslate("MDP_Formulario_LineaMovil_TextoBotonSecundario", "")

            focusMappingHandler(checkoutFieldType)

        end if
    end if
end function

sub ReadHubGateRegionFields(region, screenType, option)
    print "region and screentype:" region, screenType
    if option = "SELECT"
        if region = "argentina" or region = "peru"
            if screenType = 1
                m.top.value = {
                    opcion: "SELECT"
                    data: {
                        account: m.top.findNode("fieldMobileNumberInput").value
                    }
                }
            else
                m.top.value = {
                    opcion: "SELECT"
                    data: {
                        pin: m.top.findNode("fieldPinInput").value
                    }
                }
            end if
        else if region = "mexico"
            if screenType = 1
                m.top.value = {
                    opcion: "SELECT"
                    data: {
                        ' numberField: m.top.findNode("fieldMobileNumber").value
                        account: m.top.findNode("fieldMobileNumberInput").value
                    }
                }
            else
                m.top.value = {
                    opcion: "SELECT"
                    data: {
                        pin: m.top.findNode("fieldPinInput").value
                    }
                }
            end if
        else if region = "colombia"
            m.top.value = {
                opcion: "SELECT"
                data: {
                    fieldDocument: m.top.findNode("fieldDocumentInput").value
                    document: m.top.findNode("fieldDocumentNumberInput").value
                    account: m.top.findNode("fieldFixedNumberInput").value
                }
            }
        else
            print "ELSE OTHER SHOULD CALL"
            if screenType = 1
                m.top.value = {
                    opcion: "SELECT"
                    data: {
                        fieldMovilID: m.top.findNode("fieldMovilIDInput").value
                        account: m.top.findNode("fieldMobileNumberInput").value
                    }
                }
            else
                m.top.value = {
                    opcion: "SELECT"
                    data: {
                        pin: m.top.findNode("fieldPinInput").value
                    }
                }
            end if
        end if
    else if option = "BACK"
        m.top.value = {
            opcion: "BACK"
            data: ""
        }
    else if option = "PIN"
        m.top.value = {
            opcion: "PIN"
            data: {
                pin: m.top.findNode("fieldPinInput").value
            }
        }
    else
    end if
end sub


sub callDocTypeGrid()
    print "inside your document type"
    documentTypeGrid = CreateObject("roSGNode", "CustomMarkGrid")
    documentTypeGrid.ObserveField("selected", "setDocumentTypeField")
    documentTypeGrid.id = "documentTypeGrid"
    m.top.routerChild = {
        page: documentTypeGrid,
        fields: {
            gridType: "documentType"
            gridData: ["CARNET DIPLOMÁTICO", "CÉDULA CIUDADANÍA", "CÉDULA DE EXTRANJERÍA", "NIT", "PASAPORTE"]
        }
    }
end sub

sub setDocumentTypeField(event)
    data = event.getData()
    print "setDocumentTypeField:" data
    m.top.findNode("fieldDocumentInput").value = data
end sub


