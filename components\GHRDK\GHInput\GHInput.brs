' GHLabel
'
' by<PERSON><PERSON>e(2020) <EMAIL>

function init()
  ' seteos generales
  m.top.focusable = true
  ' componentes

  m.border = m.top.findNode("border")
  m.border.uri = ghGetImageByMode("focus01.9.png")

  m.GHInputLayout = m.top.findNode("GHInputLayout")

  m.inDialog = false ' para el input
  m.dialog = m.top.GetScene().dialog


  m.input = m.top.findNode("input")
  m.input.focusable = true
  m.input.clearOnDownKey = false
  m.input.setFocus(true)

  m.dropDownIcon = m.top.findNode("dropDownIcon")

  recalcSizeAndPadding()

end function

'
' EVENTS
' -----------------------------
function onKeyEvent(key, press) as boolean
  handled = false
  if press then
    if key = "OK" then
      if m.top.debug then print ghLogHead(); "onKeyEvent -- OK!";
      if m.top.keyboardType = "normal"
        InputDialog(m.input.text)
        m.top.GetScene().dialog.ObserveField("wasClosed", "termino")
        m.top.GetScene().dialog.ObserveField("buttonSelected", "resultado")
        handled = true
      else
        m.top.selected = true
        handled = true
        print "HANDLE YOUR LOGIC"
      end if
    else
      if m.top.debug then print ghLogHead(); "onKeyEvent -- bubbling.. ";
    end if
    if m.top.debug then print " | "; m.top.id;" | " key;" | " press
  end if
  return handled
end function

sub InputDialog(texto = "" as string)
  ' result = ""
  dialog = CreateObject("roSGNode", "StandardKeyboardDialog")
  dialog.palette = createObject("roSGNode", "RSGPalette")
  ' Defino la paleta de colores para el teclado
  dialog.palette.colors = {
    DialogBackgroundColor: "#383838",
    DialogItemColor: "#4c4c4c",
    DialogTextColor: "#FFFFFF",
    DialogFocusColor: "#981C15",
    DialogFocusItemColor: "#FFFFFF",
    DialogSecondaryTextColor: "#FFFFFF",
    DialogSecondaryItemColor: "#FFFFFF",
    DialogInputFieldColor: "#4c4c4c",
    DialogKeyboardColor: "#4c4c4c",
    DialogFootprintColor: "#4c4c4c"
  }
  dialog.text = texto
  dialog.title = m.top.title
  dialog.message = [m.top.message]
  dialog.textEditBox.hintText = m.top.placeholder
  dialog.textEditBox.secureMode = m.top.password
  dialog.textEditBox.cursorPosition = Len(texto)
  dialog.textEditBox.maxTextLength = m.top.maxTextLength
  if m.top.password then
    dialog.buttons = [ghTranslate("generic_keyboard_button_next", "Continuar"), ghTranslate("generic_keyboard_button_cancel", "Cancelar"), ghTranslate("generic_keyboard_button_show_password", "Mostrar contraseña")]
  else
    dialog.buttons = [ghTranslate("generic_keyboard_button_next", "Continuar"), ghTranslate("generic_keyboard_button_cancel", "Cancelar")]
  end if
  m.top.GetScene().dialog = dialog
end sub

function resultado() as boolean
  dialog = m.top.GetScene().dialog
  print
  print "----------------------------"
  print "RESULTADO -- Button [";dialog.buttonSelected;"] Text [";dialog.text;"]"
  if dialog.buttonSelected = 0 then ' *** OK ***
    m.input.text = dialog.text ' solo si dio OK
    dialog.close = true
  else if dialog.buttonSelected = 1 then ' *** Cancel ***
    dialog.close = true
  else if dialog.buttonSelected = 2 then ' *** mostrar/ocultar password **
    showHidePassword()
  end if
  print "----------------------------"
  return true
end function

function showHidePassword()
  dialog = m.top.GetScene().dialog
  if dialog.textEditBox.secureMode then
    m.input.secureMode = false
    dialog.textEditBox.secureMode = false
    dialog.buttons = [ghTranslate("generic_keyboard_button_next", "Continuar"), ghTranslate("generic_keyboard_button_cancel", "Cancelar"), ghTranslate("generic_keyboard_button_hide_password", "Ocultar contraseña")]
  else
    m.input.secureMode = true
    dialog.textEditBox.secureMode = true
    dialog.buttons = [ghTranslate("generic_keyboard_button_next", "Continuar"), ghTranslate("generic_keyboard_button_cancel", "Cancelar"), ghTranslate("generic_keyboard_button_show_password", "Mostrar contraseña")]
  end if
  dialog.setFocus(true)
  return true
end function

function termino() as boolean
  dialog = m.top.GetScene().dialog
  print
  print "----------------------------"
  print "TERMINO -- Button ";dialog.buttonSelected
  ' m.top.GetScene().dialog = invalid ' mato el objeto, parece que no hace falta
  print "----------------------------"
  return true
end function

sub updateHeaderText()
  if m.top.headerText <> ""
    Label = CreateObject("roSGNode", "Label")
    Label.setFields({
      id: "title"
      text: m.top.headerText
      visible: true
      color: "0xFFFFFF"
      horizAlign: "left"
      width: m.top.width
      font: ghGetFont(handlingSizeForHD(32), "regular")
    })
    m.GHInputLayout.insertChild(Label, 0)
    m.dropDownIcon.translation = [m.input.width - m.dropDownIcon.width, ((m.input.height - m.dropDownIcon.height) / 2) + 40]
  else
    m.dropDownIcon.translation = [m.input.width - m.dropDownIcon.width, ((m.input.height - m.dropDownIcon.height) / 2) + 13]
  end if
end sub















' END FILE ------------------