sub Init()
  m.top.debug = false

  m.top.getScene().updateTheme = m.global.config.theme

  m.title = m.top.findNode("title")
  m.title.font = ghGetFont(40, "regular")
  m.title.text = ghTranslate("login_access_title_label", "Inicia sesión")

  m.descrip = m.top.findNode("descrip")
  m.descrip.font = ghGetFont(28, "regular")
  m.descrip.text = ghTranslate("login_access_description_label", "¿Cuál es tu correo electrónico?")

  m.user = m.top.findNode("user")
  m.user.placeholder = ghTranslate("login_access_placeHolder_textfield", "Correo electrónico")
  getRokuUserEmail("gotRokuUserEmail")

  m.pass = m.top.findNode("pass")
  m.pass.placeholder = ghTranslate("login_password_placeHolder_textfield", "Contraseña")
  m.pass.message = ghTranslate("login_password_tooltip_text", "Ingresa tu contraseña")

  ' -------------- ARGENTINA -----------------
  ' m.user.value = "<EMAIL>"
  ' m.user.value = "<EMAIL>"
  ' m.user.value = "<EMAIL>"
  ' m.user.value = "<EMAIL>"
  ' m.user.value = "<EMAIL>"
  ' m.user.value = "<EMAIL>"
  ' m.user.value = "<EMAIL>"
  ' m.user.value = "<EMAIL>"
  ' m.user.value = "<EMAIL>"
  ' m.user.value = "<EMAIL>"
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Claro123"
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Claro2021"

  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Amco2023"

  ' m.user.value = "<EMAIL>"
  ' m.pass.value ="Amco2024"

  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Claro123@"

  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "LJanet87"

  ' ---------------------------------------

  ' -------------- MEXICO -----------------
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Claro123"

  'm.user.value = "<EMAIL>"
  'm.pass.value = "Prueba02"
  ' ---------------------------------------

  ' -------------- LITE -----------------
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Claro123"
  ' m.user.value = "<EMAIL>" ' pin parental
  ' m.pass.value = "Amco2023"

  ' pruebas de pin
  ' m.user.value = "<EMAIL>" ' pin parental
  ' m.pass.value = "Amco2024"

  ' -------------- nuevos ok ---------------
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Amco1234"
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Nm_12345"
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Amco1234"

  ' preuat
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "LJanet87"
  ' uat mex
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Camila16"
  ' arg
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "A135alf!"
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Camila16"

  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Abcd!1234"

  ' m.user.value = "<EMAIL>
  ' m.pass.value = "LJanet87"

  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Claro123"

  ' ---------------------------------------

  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Claro123"
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Claro12345"

  ' ---------------------------------------
  'GUATEMELA PRE_UAT
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Claro123"

  'COLOMBIA PRE_UAT
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Claro123"

  'MEXICO PRE_UAT
  ' m.user.value = "<EMAIL>"
  ' m.pass.value = "Claro123"

  m.user.value = "<EMAIL>"
  m.pass.value = "Claro123"

  m.login = m.top.findNode("login")
  m.login.text = ghTranslate("login_access_option_button_next", "SIGUIENTE")

  m.cancel = m.top.findNode("cancel")
  m.cancel.text = ghTranslate("login_access_option_button_cancel", "CANCELAR")

  m.toRegister = m.top.findNode("toRegister")
  m.toRegister.text = ghTranslate("login_access_option_button_register", "¿Nuevo en Claro video? Regístrate", {})
  m.toRegister.font = ghGetFont(16, "regular")
  m.toRegister.wrap = true

  m.loading = m.top.findNode("loading")

  m.botonera = m.top.findNode("botonera")
  m.botonera.ObserveField("selected", "OnButtonSelected")
  m.botonera.ObserveField("backSelected", "BackTo")
  m.botonera.map = {
    "user": { "up": "toRegister", "right": invalid, "down": "pass", "left": invalid },
    "pass": { "up": "user", "right": invalid, "down": "login", "left": invalid },
    "login": { "up": "pass", "right": invalid, "down": "cancel", "left": invalid }
    "cancel": { "up": "login", "right": invalid, "down": "toRegister", "left": invalid }
    "toRegister": { "up": "cancel", "right": invalid, "down": "user", "left": invalid }
  }

  m.error = m.top.findNode("error")
  m.error.ObserveField("wasClosed", "BackFromError")
end sub

sub gotRokuUserEmail()
  if m.store.userData <> invalid then
    if m.store.userData.email <> invalid then
      m.user.value = m.store.userData.email
    end if
  end if
end sub

sub onWasShown(event)
  data = event.getData()

  m.top.signalBeacon("AppDialogInitiate")

  GA4Event("screen_view", {
    screen_name: "login",
    screen_class: "/login"
    user_type: getUserTypeGA4(true),
  })

  if m.top.debug then print ghLogHead();"onWasShown -- init - ";m.top.focus

  if data then
    turnFocusTo("botonera")
  end if
end sub

sub updateFieldFocus()
  if m.top.debug then print ghLogHead();"updateFieldFocus -- init - ";m.top.focus

  if m.top.focus then
    turnFocusTo("botonera")
  end if
end sub

sub OnButtonSelected(event)
  child = event.getRoSGNode()

  if m.top.debug then print ghLogHead();"OnButtonSelected -- value=";child.value

  if child.selected then
    child.selected = false

    if child.value = "Login" then
      ghTurnLoading(true, m.loading, m.botonera)

      if m.top.debug then print ghLogHead();">>> Login --- [FlujoLogin]"

      m.apiLogin = ghCallApi("Login", "FlujoLogin", "FlujoLogin_Error")
      m.apiLogin.username = m.top.findNode("user").value
      m.apiLogin.password = m.top.findNode("pass").value
      m.apiLogin.control = "run"

    else if child.value = "RegisterPage" then
      m.top.routerJump = { page: "RegisterPage" }

    else if child.value = "Back" then
      BackTo()
    end if
  end if
end sub

sub FlujoLogin(event)
  root = event.getRoSGNode()
  username = ghGetChild(root, "username", "email")
  GA4Event("login", {
    method: "email"
    user_id: ghGetRegistry("user_id", "user"),
    user_type: getUserTypeGA4(true),
    country: getCountryCode(ghGetRegistry("country_code", "user")),
    screen_name: "login",
    screen_class: "/login"
  })
  ghCallApi("PushSession")

  ' si cambia la region recargo las configuracion de apa
  if root.content.ReloadTranslations = true then
    ghCallApi("Apa", "JumpToHome", "ErrorInicializacion")
  else
    JumpToHome()
  end if
end sub

sub ErrorInicializacion()
  ghTurnLoading(false, m.loading, m.botonera)

  m.error.title = "Hubo un error inesperado"
  m.error.descrip = "Por el momento no pudimos completar la acción. Por favor, intenta más tarde. (MS-01)"

  turnFocusTo("error")
end sub

sub FlujoLogin_Error()
  ghTurnLoading(false, m.loading, m.botonera)

  m.error.title = m.apiLogin.error.error_code
  m.error.descrip = m.apiLogin.error.error_msg

  m.error.visible = true ' fix, con el cambio de turnFocus, si no esta prendido no anda.
  turnFocusTo("error")
end sub

sub JumpToHome()
  scene = m.top.getScene()
  scene.callFunc("warmReboot", {})

  Notify_Roku("Roku_Authenticated")
  m.top.signalBeacon("AppDialogComplete")

end sub

sub BackFromError()
  turnFocusTo("botonera")
end sub

sub BackTo()
  if m.top.debug then print ghLogHead();"BackTo."

  m.top.signalBeacon("AppDialogComplete")

  m.top.routerClose = true
end sub

function onKeyEvent(key, press) as boolean
  handled = false

  return handled
end function